import { createRouter, createWebHistory } from 'vue-router'
import Dashboard from '../views/Dashboard.vue'
import DataCollection from '../components/DataCollection.vue'
import EfficiencyMetrics from '../components/EfficiencyMetrics.vue'

const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard
  },
  {
    path: '/data-collection',
    name: 'DataCollection',
    component: DataCollection
  },
  {
    path: '/metrics',
    name: 'EfficiencyMetrics',
    component: EfficiencyMetrics
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

export default router