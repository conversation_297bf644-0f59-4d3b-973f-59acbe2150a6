<template>
  <div class="dashboard">
    <header class="dashboard-header">
      <div class="header-content">
        <h1>量化前端基建效率提升与成本节约</h1>

        <!-- 筛选器区域 -->
        <div class="filters-section">
          <div class="filter-group">
            <label for="organization">组织:</label>
            <select id="organization" v-model="selectedOrganization" @change="onOrganizationChange">
              <option value="all">全部组织</option>
              <option v-for="org in organizations" :key="org.id" :value="org.id">
                {{ org.name }}
              </option>
            </select>
          </div>

          <div class="filter-group">
            <label for="timeRange">时间范围:</label>
            <select id="timeRange" v-model="selectedTimeRange" @change="onTimeRangeChange">
              <option value="7d">最近7天</option>
              <option value="30d">最近30天</option>
              <option value="90d">最近90天</option>
              <option value="6m">最近6个月</option>
              <option value="1y">最近1年</option>
              <option value="custom">自定义</option>
            </select>
          </div>

          <!-- 自定义时间选择器 -->
          <div v-if="selectedTimeRange === 'custom'" class="custom-date-range">
            <div class="date-input-group">
              <label for="startDate">开始日期:</label>
              <input
                type="date"
                id="startDate"
                v-model="customStartDate"
                @change="onCustomDateChange"
              >
            </div>
            <div class="date-input-group">
              <label for="endDate">结束日期:</label>
              <input
                type="date"
                id="endDate"
                v-model="customEndDate"
                @change="onCustomDateChange"
              >
            </div>
          </div>

          <!-- 应用筛选按钮 -->
          <button class="apply-filters-btn" @click="applyFilters">
            <span class="btn-icon">🔍</span>
            应用筛选
          </button>

          <!-- 重置筛选按钮 -->
          <button class="reset-filters-btn" @click="resetFilters">
            <span class="btn-icon">🔄</span>
            重置
          </button>
        </div>
      </div>
    </header>

    <!-- 北极星指标概览 -->
    <div class="overview-section">
      <h2>北极星指标</h2>
      <div class="metric-cards">
        <div class="metric-card efficiency">
          <div class="metric-icon">⚡</div>
          <div class="metric-content">
            <h3>累计节约人天数</h3>
            <div class="metric-value">{{ totalPDSaved }} PD</div>
            <div class="metric-trend positive">+{{ monthlyPDSaved }} PD/月</div>
          </div>
        </div>
        <div class="metric-card cost">
          <div class="metric-icon">💰</div>
          <div class="metric-content">
            <h3>累计成本节约</h3>
            <div class="metric-value">¥{{ totalCostSaved.toLocaleString() }}</div>
            <div class="metric-trend positive">+¥{{ monthlyCostSaved.toLocaleString() }}/月</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 提效维度统计 -->
    <div class="efficiency-section">
      <h2>提效维度统计</h2>
      
      <!-- 一级过程指标 -->
      <div class="level-one-metrics">
        <h3>一级过程指标</h3>
        <div class="metrics-grid">
          <div class="metric-item" v-for="metric in efficiencyL1Metrics" :key="metric.name">
            <div class="metric-header">
              <span class="metric-name">{{ metric.name }}</span>
              <span class="metric-target">目标: {{ metric.target }}</span>
            </div>
            <div class="metric-progress">
              <div class="progress-bar">
                <div class="progress-fill" :style="{width: metric.progress + '%'}"></div>
              </div>
              <span class="progress-text">{{ metric.current }} / {{ metric.target }}</span>
            </div>
            <div class="metric-details">
              <span class="data-source">数据来源: {{ metric.dataSource }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 二级过程指标 -->
      <div class="level-two-metrics">
        <h3>二级过程指标</h3>
        
        <!-- 工程构建类 -->
        <div class="metric-category">
          <h4>工程构建类</h4>
          <div class="metrics-table">
            <table>
              <thead>
                <tr>
                  <th>指标名称</th>
                  <th>当前值</th>
                  <th>目标值</th>
                  <th>完成率</th>
                  <th>数据来源</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="metric in buildMetrics" :key="metric.name">
                  <td>{{ metric.name }}</td>
                  <td>{{ metric.current }}</td>
                  <td>{{ metric.target }}</td>
                  <td>
                    <span :class="['completion-rate', metric.status]">
                      {{ metric.completionRate }}%
                    </span>
                  </td>
                  <td>{{ metric.dataSource }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 线上问题处理类 -->
        <div class="metric-category">
          <h4>线上问题处理类</h4>
          <div class="metrics-table">
            <table>
              <thead>
                <tr>
                  <th>指标名称</th>
                  <th>当前值</th>
                  <th>目标值</th>
                  <th>完成率</th>
                  <th>数据来源</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="metric in problemMetrics" :key="metric.name">
                  <td>{{ metric.name }}</td>
                  <td>{{ metric.current }}</td>
                  <td>{{ metric.target }}</td>
                  <td>
                    <span :class="['completion-rate', metric.status]">
                      {{ metric.completionRate }}%
                    </span>
                  </td>
                  <td>{{ metric.dataSource }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 组件复用类 -->
        <div class="metric-category">
          <h4>组件复用类</h4>
          <div class="metrics-table">
            <table>
              <thead>
                <tr>
                  <th>指标名称</th>
                  <th>当前值</th>
                  <th>目标值</th>
                  <th>完成率</th>
                  <th>数据来源</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="metric in componentMetrics" :key="metric.name">
                  <td>{{ metric.name }}</td>
                  <td>{{ metric.current }}</td>
                  <td>{{ metric.target }}</td>
                  <td>
                    <span :class="['completion-rate', metric.status]">
                      {{ metric.completionRate }}%
                    </span>
                  </td>
                  <td>{{ metric.dataSource }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- 成本维度统计 -->
    <div class="cost-section">
      <h2>成本维度统计</h2>
      
      <!-- 一级过程指标 -->
      <div class="cost-overview">
        <h3>一级过程指标</h3>
        <div class="cost-metrics-grid">
          <div class="cost-metric-card" v-for="metric in costL1Metrics" :key="metric.name">
            <div class="cost-metric-header">
              <h4>{{ metric.name }}</h4>
              <span class="cost-target">目标: {{ metric.target }}</span>
            </div>
            <div class="cost-metric-body">
              <div class="cost-value">
                <span class="current-value">¥{{ metric.currentSaving.toLocaleString() }}</span>
                <span class="optimization-rate" :class="metric.status">
                  {{ metric.optimizationRate }}%
                </span>
              </div>
              <div class="cost-progress">
                <div class="progress-bar">
                  <div class="progress-fill" :style="{width: metric.progress + '%'}"></div>
                </div>
              </div>
              <div class="cost-source">{{ metric.dataSource }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 二级过程指标 -->
      <div class="cost-detail">
        <h3>二级过程指标</h3>
        <div class="cost-detail-table">
          <table>
            <thead>
              <tr>
                <th>指标名称</th>
                <th>当前节约金额</th>
                <th>目标值</th>
                <th>完成率</th>
                <th>数据来源</th>
                <th>趋势</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="metric in costL2Metrics" :key="metric.name">
                <td>{{ metric.name }}</td>
                <td>¥{{ metric.currentSaving.toLocaleString() }}</td>
                <td>{{ metric.target }}</td>
                <td>
                  <span :class="['completion-rate', metric.status]">
                    {{ metric.completionRate }}%
                  </span>
                </td>
                <td>{{ metric.dataSource }}</td>
                <td>
                  <span :class="['trend', metric.trend]">
                    {{ metric.trendText }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 重点执行指标 -->
    <div class="key-metrics-section">
      <h2>重点执行指标</h2>
      <div class="key-metrics-grid">
        <div class="key-metric-card" v-for="metric in keyMetrics" :key="metric.name">
          <div class="key-metric-icon">📊</div>
          <div class="key-metric-content">
            <h4>{{ metric.name }}</h4>
            <div class="key-metric-value">{{ metric.current }}</div>
            <div class="key-metric-target">目标: {{ metric.target }}</div>
            <div class="key-metric-source">{{ metric.dataSource }}</div>
          </div>
          <div class="key-metric-status" :class="metric.status">
            {{ metric.statusText }}
          </div>
        </div>
      </div>
    </div>

    <!-- 数据采集状态 -->
    <div class="data-collection-section">
      <h2>数据采集状态</h2>
      <div class="collection-status">
        <div class="status-item" v-for="source in dataSources" :key="source.name">
          <div class="status-indicator" :class="source.status"></div>
          <span class="source-name">{{ source.name }}</span>
          <span class="last-update">最后更新: {{ source.lastUpdate }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DashboardView',
  data() {
    return {
      // 筛选器相关数据
      selectedOrganization: 'all',
      selectedTimeRange: '30d',
      customStartDate: '',
      customEndDate: '',
      
      // 组织列表
      organizations: [
        { id: 'frontend', name: '前端团队' },
        { id: 'backend', name: '后端团队' },
        { id: 'mobile', name: '移动端团队' },
        { id: 'devops', name: 'DevOps团队' },
        { id: 'qa', name: '测试团队' },
        { id: 'design', name: '设计团队' }
      ],
      
      // 北极星指标
      totalPDSaved: 156.5,
      monthlyPDSaved: 28.3,
      totalCostSaved: 1250000,
      monthlyCostSaved: 185000,
      
      // 提效维度一级指标
      efficiencyL1Metrics: [
        {
          name: '构建提效PD',
          current: '22.5 PD/月',
          target: '>20 PD/月',
          progress: 112,
          dataSource: '构建系统、部署平台'
        },
        {
          name: '问题提效PD',
          current: '18.2 PD/月',
          target: '>15 PD/月',
          progress: 121,
          dataSource: '监控系统、工单系统'
        },
        {
          name: '组件提效PD',
          current: '12.8 PD/月',
          target: '>10 PD/月',
          progress: 128,
          dataSource: '代码仓库、组件库'
        }
      ],
      
      // 工程构建类指标
      buildMetrics: [
        {
          name: '构建速度PD',
          current: '9.2 PD/月',
          target: '>8 PD/月',
          completionRate: 115,
          status: 'success',
          dataSource: '构建系统日志'
        },
        {
          name: '部署效率PD',
          current: '7.8 PD/月',
          target: '>6 PD/月',
          completionRate: 130,
          status: 'success',
          dataSource: '部署平台日志'
        },
        {
          name: '开发体验PD',
          current: '5.5 PD/月',
          target: '>4 PD/月',
          completionRate: 137,
          status: 'success',
          dataSource: 'IDE插件统计'
        }
      ],
      
      // 线上问题处理类指标
      problemMetrics: [
        {
          name: '发现提效PD',
          current: '6.2 PD/月',
          target: '>5 PD/月',
          completionRate: 124,
          status: 'success',
          dataSource: '监控系统'
        },
        {
          name: '定位提效PD',
          current: '9.5 PD/月',
          target: '>8 PD/月',
          completionRate: 118,
          status: 'success',
          dataSource: '工单系统'
        },
        {
          name: '解决提效PD',
          current: '2.5 PD/月',
          target: '>3 PD/月',
          completionRate: 83,
          status: 'warning',
          dataSource: '工单系统'
        }
      ],
      
      // 组件复用类指标
      componentMetrics: [
        {
          name: '开发提效PD',
          current: '4.2 PD/月',
          target: '>3 PD/月',
          completionRate: 140,
          status: 'success',
          dataSource: '项目管理工具'
        },
        {
          name: '复用节约PD',
          current: '6.8 PD/月',
          target: '>6 PD/月',
          completionRate: 113,
          status: 'success',
          dataSource: '代码分析工具'
        },
        {
          name: '维护提效PD',
          current: '1.8 PD/月',
          target: '>2 PD/月',
          completionRate: 90,
          status: 'warning',
          dataSource: '版本管理系统'
        }
      ],
      
      // 成本维度一级指标
      costL1Metrics: [
        {
          name: 'CDN节约',
          currentSaving: 45000,
          target: '>10%',
          optimizationRate: 12.5,
          progress: 125,
          status: 'success',
          dataSource: 'CDN服务商账单'
        },
        {
          name: '计算资源节约',
          currentSaving: 85000,
          target: '>15%',
          optimizationRate: 18.2,
          progress: 121,
          status: 'success',
          dataSource: '云服务商账单'
        },
        {
          name: '存储节约',
          currentSaving: 28000,
          target: '>8%',
          optimizationRate: 9.8,
          progress: 122,
          status: 'success',
          dataSource: '云服务商账单'
        },
        {
          name: '带宽节约',
          currentSaving: 27000,
          target: '>12%',
          optimizationRate: 14.5,
          progress: 120,
          status: 'success',
          dataSource: '云服务商账单'
        }
      ],
      
      // 成本维度二级指标
      costL2Metrics: [
        {
          name: '缓存节约',
          currentSaving: 6200,
          target: '>5,000元/月',
          completionRate: 124,
          status: 'success',
          dataSource: 'CDN服务商',
          trend: 'up',
          trendText: '↗ +8.5%'
        },
        {
          name: '压缩节约',
          currentSaving: 3800,
          target: '>3,000元/月',
          completionRate: 126,
          status: 'success',
          dataSource: 'CDN服务商',
          trend: 'up',
          trendText: '↗ +12.3%'
        },
        {
          name: '分发节约',
          currentSaving: 2100,
          target: '>2,000元/月',
          completionRate: 105,
          status: 'success',
          dataSource: 'CDN服务商',
          trend: 'stable',
          trendText: '→ +2.1%'
        },
        {
          name: 'CPU节约',
          currentSaving: 9200,
          target: '>8,000元/月',
          completionRate: 115,
          status: 'success',
          dataSource: '云服务商',
          trend: 'up',
          trendText: '↗ +15.2%'
        },
        {
          name: '内存节约',
          currentSaving: 6800,
          target: '>6,000元/月',
          completionRate: 113,
          status: 'success',
          dataSource: '云服务商',
          trend: 'up',
          trendText: '↗ +9.8%'
        },
        {
          name: '实例节约',
          currentSaving: 3800,
          target: '>4,000元/月',
          completionRate: 95,
          status: 'warning',
          dataSource: '云服务商',
          trend: 'down',
          trendText: '↘ -2.5%'
        }
      ],
      
      // 重点执行指标
      keyMetrics: [
        {
          name: '增量编译节约',
          current: '68%',
          target: '>60%',
          dataSource: '构建系统',
          status: 'success',
          statusText: '达标'
        },
        {
          name: '告警准确率',
          current: '92%',
          target: '>90%',
          dataSource: '监控系统',
          status: 'success',
          statusText: '达标'
        },
        {
          name: '组件使用频次',
          current: '58次/组件',
          target: '>50次/组件',
          dataSource: '代码分析工具',
          status: 'success',
          statusText: '达标'
        }
      ],
      
      // 数据源状态
      dataSources: [
        {
          name: '构建系统',
          status: 'online',
          lastUpdate: '2024-06-20 15:30:00'
        },
        {
          name: '部署平台',
          status: 'online',
          lastUpdate: '2024-06-20 15:28:00'
        },
        {
          name: '监控系统',
          status: 'online',
          lastUpdate: '2024-06-20 15:32:00'
        },
        {
          name: '工单系统',
          status: 'warning',
          lastUpdate: '2024-06-20 14:45:00'
        },
        {
          name: 'CDN服务商',
          status: 'online',
          lastUpdate: '2024-06-20 15:25:00'
        },
        {
          name: '云服务商',
          status: 'online',
          lastUpdate: '2024-06-20 15:20:00'
        }
      ]
    }
  },
  
  methods: {
    // 组织变更处理
    onOrganizationChange() {
      console.log('组织切换到:', this.selectedOrganization)
      this.loadDataByFilters()
    },
    
    // 时间范围变更处理
    onTimeRangeChange() {
      console.log('时间范围切换到:', this.selectedTimeRange)
      if (this.selectedTimeRange !== 'custom') {
        this.loadDataByFilters()
      }
    },
    
    // 自定义日期变更处理
    onCustomDateChange() {
      if (this.customStartDate && this.customEndDate) {
        console.log('自定义时间范围:', this.customStartDate, '到', this.customEndDate)
        this.loadDataByFilters()
      }
    },
    
    // 应用筛选
    applyFilters() {
      console.log('应用筛选条件:', {
        organization: this.selectedOrganization,
        timeRange: this.selectedTimeRange,
        customStartDate: this.customStartDate,
        customEndDate: this.customEndDate
      })
      this.loadDataByFilters()
    },
    
    // 重置筛选
    resetFilters() {
      this.selectedOrganization = 'all'
      this.selectedTimeRange = '30d'
      this.customStartDate = ''
      this.customEndDate = ''
      console.log('筛选条件已重置')
      this.loadDataByFilters()
    },
    
    // 根据筛选条件加载数据
    loadDataByFilters() {
      // 这里可以根据筛选条件调用API获取数据
      // 目前只是模拟数据更新
      const orgMultiplier = this.getOrgMultiplier()
      const timeMultiplier = this.getTimeMultiplier()
      
      // 更新北极星指标
      this.totalPDSaved = (156.5 * orgMultiplier * timeMultiplier).toFixed(1)
      this.monthlyPDSaved = (28.3 * orgMultiplier).toFixed(1)
      this.totalCostSaved = Math.round(1250000 * orgMultiplier * timeMultiplier)
      this.monthlyCostSaved = Math.round(185000 * orgMultiplier)
      
      console.log('数据已根据筛选条件更新')
    },
    
    // 获取组织系数
    getOrgMultiplier() {
      const multipliers = {
        'all': 1.0,
        'frontend': 0.4,
        'backend': 0.3,
        'mobile': 0.15,
        'devops': 0.1,
        'qa': 0.03,
        'design': 0.02
      }
      return multipliers[this.selectedOrganization] || 1.0
    },
    
    // 获取时间系数
    getTimeMultiplier() {
      const multipliers = {
        '7d': 0.25,
        '30d': 1.0,
        '90d': 3.0,
        '6m': 6.0,
        '1y': 12.0,
        'custom': 1.0
      }
      return multipliers[this.selectedTimeRange] || 1.0
    },
    
    // 格式化日期显示
    formatDate(date) {
      return new Date(date).toLocaleDateString('zh-CN')
    }
  },
  
  mounted() {
    // 组件挂载时设置默认的自定义日期
    const today = new Date()
    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
    
    this.customEndDate = today.toISOString().split('T')[0]
    this.customStartDate = thirtyDaysAgo.toISOString().split('T')[0]
  }
}
</script>

<style scoped>
.dashboard {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 页面标题样式 */
.dashboard-header {
  margin-bottom: 40px;
  padding: 30px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
}

.dashboard-header h1 {
  font-size: 2rem;
  margin-bottom: 30px;
  font-weight: 600;
  color: #2c3e50;
}

/* 筛选器样式 */
.filters-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 15px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  white-space: nowrap;
}

.filter-group select {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
  color: #495057;
  font-size: 14px;
  min-width: 120px;
  transition: all 0.3s ease;
}

.filter-group select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
}

.custom-date-range {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.date-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-input-group label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  white-space: nowrap;
}

.date-input-group input[type="date"] {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
  color: #495057;
  font-size: 14px;
  transition: all 0.3s ease;
}

.date-input-group input[type="date"]:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
}

.apply-filters-btn,
.reset-filters-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.apply-filters-btn {
  background: #28a745;
  color: white;
  border: 1px solid #28a745;
}

.apply-filters-btn:hover {
  background: #218838;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.reset-filters-btn {
  background: #dc3545;
  color: white;
  border: 1px solid #dc3545;
}

.reset-filters-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.btn-icon {
  font-size: 12px;
}



/* 北极星指标样式 */
.overview-section {
  margin-bottom: 40px;
}

.overview-section h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.metric-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
}

.metric-card.efficiency {
  border-left: 4px solid #e74c3c;
}

.metric-card.cost {
  border-left: 4px solid #27ae60;
}

.metric-icon {
  font-size: 3em;
}

.metric-content h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 1.1em;
}

.metric-value {
  font-size: 2.5em;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.metric-trend {
  font-size: 1.1em;
  font-weight: 500;
}

.metric-trend.positive {
  color: #27ae60;
}

/* 提效维度样式 */
.efficiency-section {
  margin-bottom: 40px;
}

.efficiency-section h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  border-bottom: 2px solid #e74c3c;
  padding-bottom: 10px;
}

.level-one-metrics {
  margin-bottom: 30px;
}

.level-one-metrics h3 {
  color: #34495e;
  margin-bottom: 15px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.metric-item {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.metric-name {
  font-weight: bold;
  color: #2c3e50;
}

.metric-target {
  color: #7f8c8d;
  font-size: 0.9em;
}

.metric-progress {
  margin-bottom: 10px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 5px;
}

.progress-fill {
  height: 100%;
  background-color: #e74c3c;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.9em;
  color: #7f8c8d;
}

.metric-details {
  font-size: 0.8em;
  color: #95a5a6;
}

/* 二级指标表格样式 */
.level-two-metrics h3 {
  color: #34495e;
  margin-bottom: 20px;
}

.metric-category {
  margin-bottom: 30px;
}

.metric-category h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  padding-left: 10px;
  border-left: 3px solid #e74c3c;
}

.metrics-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.metrics-table table {
  width: 100%;
  border-collapse: collapse;
}

.metrics-table th {
  background-color: #f8f9fa;
  padding: 15px;
  text-align: left;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 1px solid #dee2e6;
}

.metrics-table td {
  padding: 15px;
  border-bottom: 1px solid #dee2e6;
}

.completion-rate {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.9em;
  font-weight: 500;
}

.completion-rate.success {
  background-color: #d4edda;
  color: #155724;
}

.completion-rate.warning {
  background-color: #fff3cd;
  color: #856404;
}

/* 成本维度样式 */
.cost-section {
  margin-bottom: 40px;
}

.cost-section h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  border-bottom: 2px solid #27ae60;
  padding-bottom: 10px;
}

.cost-overview h3 {
  color: #34495e;
  margin-bottom: 15px;
}

.cost-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.cost-metric-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #27ae60;
}

.cost-metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.cost-metric-header h4 {
  margin: 0;
  color: #2c3e50;
}

.cost-target {
  color: #7f8c8d;
  font-size: 0.9em;
}

.cost-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.current-value {
  font-size: 1.5em;
  font-weight: bold;
  color: #2c3e50;
}

.optimization-rate {
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.optimization-rate.success {
  background-color: #d4edda;
  color: #155724;
}

.cost-progress .progress-fill {
  background-color: #27ae60;
}

.cost-source {
  font-size: 0.8em;
  color: #95a5a6;
  margin-top: 10px;
}

/* 成本详细表格 */
.cost-detail {
  margin-top: 30px;
}

.cost-detail h3 {
  color: #34495e;
  margin-bottom: 15px;
}

.cost-detail-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.trend {
  font-weight: 500;
}

.trend.up {
  color: #27ae60;
}

.trend.down {
  color: #e74c3c;
}

.trend.stable {
  color: #f39c12;
}

/* 重点执行指标样式 */
.key-metrics-section {
  margin-bottom: 40px;
}

.key-metrics-section h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  border-bottom: 2px solid #9b59b6;
  padding-bottom: 10px;
}

.key-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.key-metric-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  border-left: 4px solid #9b59b6;
}

.key-metric-icon {
  font-size: 2em;
}

.key-metric-content {
  flex: 1;
}

.key-metric-content h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
}

.key-metric-value {
  font-size: 1.8em;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.key-metric-target {
  color: #7f8c8d;
  font-size: 0.9em;
  margin-bottom: 5px;
}

.key-metric-source {
  color: #95a5a6;
  font-size: 0.8em;
}

.key-metric-status {
  padding: 6px 12px;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.9em;
}

.key-metric-status.success {
  background-color: #d4edda;
  color: #155724;
}

/* 数据采集状态样式 */
.data-collection-section h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  border-bottom: 2px solid #34495e;
  padding-bottom: 10px;
}

.collection-status {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px 0;
  border-bottom: 1px solid #ecf0f1;
}

.status-item:last-child {
  border-bottom: none;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.status-indicator.online {
  background-color: #27ae60;
}

.status-indicator.warning {
  background-color: #f39c12;
}

.status-indicator.offline {
  background-color: #e74c3c;
}

.source-name {
  font-weight: 500;
  color: #2c3e50;
  min-width: 120px;
}

.last-update {
  color: #7f8c8d;
  font-size: 0.9em;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: 10px;
  }
  
  .dashboard-header h1 {
    font-size: 1.8rem;
  }
  
  /* 筛选器响应式 */
  .filters-section {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
    padding: 15px;
  }

  .filter-group {
    flex-direction: column;
    align-items: stretch;
    gap: 5px;
  }

  .filter-group select {
    min-width: auto;
    width: 100%;
  }

  .custom-date-range {
    flex-direction: column;
    gap: 10px;
  }

  .date-input-group {
    flex-direction: column;
    align-items: stretch;
    gap: 5px;
  }

  .date-input-group input[type="date"] {
    width: 100%;
  }

  .apply-filters-btn,
  .reset-filters-btn {
    width: 100%;
    justify-content: center;
  }
  
  .metric-cards {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .cost-metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .key-metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .metrics-table {
    overflow-x: auto;
  }
  
  .collection-status {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .status-item {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .filters-section {
    padding: 15px;
  }
  
  .filter-group label,
  .date-input-group label {
    font-size: 13px;
  }
  
  .filter-group select,
  .date-input-group input[type="date"] {
    font-size: 13px;
    padding: 6px 10px;
  }
  
  .apply-filters-btn,
  .reset-filters-btn {
    font-size: 13px;
    padding: 6px 12px;
  }
}
</style>