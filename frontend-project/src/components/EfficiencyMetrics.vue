<template>
  <div class="efficiency-metrics">
    <h2>效率与成本指标</h2>
    
    <!-- 指标选择器 -->
    <div class="metrics-selector">
      <div class="selector-tabs">
        <button 
          v-for="tab in metricTabs" 
          :key="tab.id"
          :class="['tab-btn', { active: activeTab === tab.id }]"
          @click="activeTab = tab.id"
        >
          {{ tab.name }}
        </button>
      </div>
    </div>
    
    <!-- 效率指标 -->
    <div v-if="activeTab === 'efficiency'" class="metrics-section">
      <div class="metrics-grid">
        <!-- 一级指标 -->
        <div 
          v-for="metric in efficiencyMetrics.level1" 
          :key="metric.id"
          class="metric-card level1"
        >
          <div class="metric-header">
            <h3>{{ metric.name }}</h3>
            <div class="metric-value" :class="getValueClass(metric)">
              {{ formatValue(metric.value) }}
              <span class="metric-unit">{{ metric.unit }}</span>
              <span 
                class="metric-trend" 
                :class="getTrendClass(metric.trend)"
              >
                {{ formatTrend(metric.trend) }}
              </span>
            </div>
          </div>
          <div class="metric-info">
            <div class="metric-desc">{{ metric.description }}</div>
            <div class="metric-source">数据来源: {{ metric.source }}</div>
          </div>
          <div class="metric-target">
            <div class="target-bar">
              <div 
                class="target-progress" 
                :style="{ width: getProgressWidth(metric) }"
              ></div>
            </div>
            <div class="target-text">
              目标值: {{ formatValue(metric.target) }} {{ metric.unit }}
            </div>
          </div>
        </div>
        
        <!-- 二级指标 -->
        <div class="level2-section">
          <h3>二级指标明细</h3>
          <div class="level2-grid">
            <div 
              v-for="metric in efficiencyMetrics.level2" 
              :key="metric.id"
              class="metric-card level2"
            >
              <div class="metric-header">
                <h4>{{ metric.name }}</h4>
                <div class="metric-value" :class="getValueClass(metric)">
                  {{ formatValue(metric.value) }}
                  <span class="metric-unit">{{ metric.unit }}</span>
                  <span 
                    class="metric-trend" 
                    :class="getTrendClass(metric.trend)"
                  >
                    {{ formatTrend(metric.trend) }}
                  </span>
                </div>
              </div>
              <div class="metric-formula">
                <span class="formula-label">计算公式:</span> {{ metric.formula }}
              </div>
              <div class="metric-target">
                <div class="target-bar">
                  <div 
                    class="target-progress" 
                    :style="{ width: getProgressWidth(metric) }"
                  ></div>
                </div>
                <div class="target-text">
                  目标值: {{ formatValue(metric.target) }} {{ metric.unit }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 成本指标 -->
    <div v-if="activeTab === 'cost'" class="metrics-section">
      <div class="metrics-grid">
        <!-- 一级指标 -->
        <div 
          v-for="metric in costMetrics.level1" 
          :key="metric.id"
          class="metric-card level1"
        >
          <div class="metric-header">
            <h3>{{ metric.name }}</h3>
            <div class="metric-value" :class="getValueClass(metric, true)">
              {{ formatValue(metric.value) }}
              <span class="metric-unit">{{ metric.unit }}</span>
              <span 
                class="metric-trend" 
                :class="getTrendClass(metric.trend, true)"
              >
                {{ formatTrend(metric.trend) }}
              </span>
            </div>
          </div>
          <div class="metric-info">
            <div class="metric-desc">{{ metric.description }}</div>
            <div class="metric-source">数据来源: {{ metric.source }}</div>
          </div>
          <div class="metric-target">
            <div class="target-bar">
              <div 
                class="target-progress" 
                :style="{ width: getProgressWidth(metric, true) }"
              ></div>
            </div>
            <div class="target-text">
              目标值: {{ formatValue(metric.target) }} {{ metric.unit }}
            </div>
          </div>
        </div>
        
        <!-- 二级指标 -->
        <div class="level2-section">
          <h3>二级指标明细</h3>
          <div class="level2-grid">
            <div 
              v-for="metric in costMetrics.level2" 
              :key="metric.id"
              class="metric-card level2"
            >
              <div class="metric-header">
                <h4>{{ metric.name }}</h4>
                <div class="metric-value" :class="getValueClass(metric, true)">
                  {{ formatValue(metric.value) }}
                  <span class="metric-unit">{{ metric.unit }}</span>
                  <span 
                    class="metric-trend" 
                    :class="getTrendClass(metric.trend, true)"
                  >
                    {{ formatTrend(metric.trend) }}
                  </span>
                </div>
              </div>
              <div class="metric-formula">
                <span class="formula-label">计算公式:</span> {{ metric.formula }}
              </div>
              <div class="metric-target">
                <div class="target-bar">
                  <div 
                    class="target-progress" 
                    :style="{ width: getProgressWidth(metric, true) }"
                  ></div>
                </div>
                <div class="target-text">
                  目标值: {{ formatValue(metric.target) }} {{ metric.unit }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 趋势图表 -->
    <div class="trend-charts">
      <h3>指标趋势分析</h3>
      <div class="chart-tabs">
        <button 
          v-for="chart in chartOptions" 
          :key="chart.id"
          :class="['chart-tab', { active: activeChart === chart.id }]"
          @click="activeChart = chart.id"
        >
          {{ chart.name }}
        </button>
      </div>
      
      <div class="chart-container">
        <!-- 这里可以集成图表库，如ECharts或Chart.js -->
        <div class="chart-placeholder">
          <div class="chart-mock">
            <div class="chart-title">{{ getActiveChartTitle() }}</div>
            <div class="chart-mock-content">
              <div 
                v-for="(bar, index) in getMockChartData()" 
                :key="index"
                class="mock-bar"
                :style="{ height: bar.height, backgroundColor: bar.color }"
              ></div>
            </div>
            <div class="chart-mock-labels">
              <span v-for="(month, index) in months" :key="index">{{ month }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EfficiencyMetrics',
  data() {
    return {
      activeTab: 'efficiency',
      activeChart: 'pd_trend',
      metricTabs: [
        { id: 'efficiency', name: '效率指标' },
        { id: 'cost', name: '成本指标' }
      ],
      chartOptions: [
        { id: 'pd_trend', name: 'PD投入趋势' },
        { id: 'cost_trend', name: '成本节约趋势' },
        { id: 'build_time', name: '构建时间趋势' },
        { id: 'issue_rate', name: '线上问题率趋势' }
      ],
      months: ['1月', '2月', '3月', '4月', '5月', '6月'],
      
      // 效率指标数据
      efficiencyMetrics: {
        level1: [
          {
            id: 'pd_efficiency',
            name: 'PD效率',
            value: 0.85,
            target: 0.9,
            unit: '',
            trend: 0.05,
            description: '衡量前端基建在提升开发效率方面的综合表现',
            source: '多维度统计数据',
            status: 'good'
          },
          {
            id: 'build_efficiency',
            name: '构建效率',
            value: 75,
            target: 90,
            unit: '%',
            trend: 0.15,
            description: '构建过程的效率提升百分比',
            source: '构建系统数据',
            status: 'good'
          },
          {
            id: 'problem_resolution',
            name: '问题解决效率',
            value: 65,
            target: 80,
            unit: '%',
            trend: -0.05,
            description: '问题解决速度的提升百分比',
            source: '问题跟踪系统',
            status: 'warning'
          }
        ],
        level2: [
          {
            id: 'build_time',
            name: '构建时间',
            value: 3.5,
            target: 2.5,
            unit: '分钟',
            trend: -0.8,
            formula: '平均构建时间',
            status: 'good'
          },
          {
            id: 'dev_setup',
            name: '开发环境搭建时间',
            value: 15,
            target: 10,
            unit: '分钟',
            trend: -5,
            formula: '平均环境搭建时间',
            status: 'good'
          },
          {
            id: 'code_reuse',
            name: '代码复用率',
            value: 65,
            target: 80,
            unit: '%',
            trend: 10,
            formula: '复用代码行数 / 总代码行数',
            status: 'good'
          },
          {
            id: 'test_coverage',
            name: '测试覆盖率',
            value: 70,
            target: 85,
            unit: '%',
            trend: 5,
            formula: '测试覆盖的代码行数 / 总代码行数',
            status: 'warning'
          },
          {
            id: 'issue_resolution',
            name: '问题解决时间',
            value: 4.2,
            target: 3,
            unit: '小时',
            trend: 0.2,
            formula: '平均问题解决时间',
            status: 'danger'
          },
          {
            id: 'deployment_frequency',
            name: '部署频率',
            value: 3.5,
            target: 5,
            unit: '次/周',
            trend: 0.5,
            formula: '每周平均部署次数',
            status: 'warning'
          }
        ]
      },
      
      // 成本指标数据
      costMetrics: {
        level1: [
          {
            id: 'cost_saving',
            name: '成本节约',
            value: 25,
            target: 30,
            unit: '%',
            trend: 5,
            description: '前端基建带来的总体成本节约比例',
            source: '财务数据与工时统计',
            status: 'good'
          },
          {
            id: 'resource_utilization',
            name: '资源利用率',
            value: 70,
            target: 85,
            unit: '%',
            trend: 8,
            description: '资源利用效率提升百分比',
            source: '资源监控系统',
            status: 'good'
          },
          {
            id: 'maintenance_cost',
            name: '维护成本',
            value: 35,
            target: 25,
            unit: '%',
            trend: -5,
            description: '维护成本占总成本的百分比',
            source: '工时与财务数据',
            status: 'warning'
          }
        ],
        level2: [
          {
            id: 'labor_cost',
            name: '人力成本',
            value: 28,
            target: 20,
            unit: '%',
            trend: -3,
            formula: '人力成本 / 总成本',
            status: 'warning'
          },
          {
            id: 'infrastructure_cost',
            name: '基础设施成本',
            value: 15,
            target: 12,
            unit: '%',
            trend: -2,
            formula: '基础设施成本 / 总成本',
            status: 'warning'
          },
          {
            id: 'error_cost',
            name: '错误修复成本',
            value: 8,
            target: 5,
            unit: '%',
            trend: -1.5,
            formula: '错误修复成本 / 总成本',
            status: 'warning'
          },
          {
            id: 'time_to_market',
            name: '上市时间',
            value: 25,
            target: 15,
            unit: '天',
            trend: -5,
            formula: '平均产品上市时间',
            status: 'good'
          },
          {
            id: 'resource_saving',
            name: '资源节约',
            value: 30,
            target: 40,
            unit: '%',
            trend: 8,
            formula: '(旧资源使用 - 新资源使用) / 旧资源使用',
            status: 'good'
          },
          {
            id: 'tech_debt',
            name: '技术债务',
            value: 20,
            target: 10,
            unit: '%',
            trend: -5,
            formula: '技术债务工时 / 总工时',
            status: 'warning'
          }
        ]
      }
    }
  },
  
  methods: {
    formatValue(value) {
      if (typeof value === 'number') {
        return value % 1 === 0 ? value : value.toFixed(1)
      }
      return value
    },
    
    formatTrend(trend) {
      if (trend === 0) return '持平'
      const prefix = trend > 0 ? '+' : ''
      return `${prefix}${trend}%`
    },
    
    getValueClass(metric, isCost = false) {
      if (isCost) {
        // 对于成本指标，值越低越好
        if (metric.value <= metric.target) return 'value-good'
        if (metric.value <= metric.target * 1.2) return 'value-warning'
        return 'value-danger'
      } else {
        // 对于效率指标，值越高越好
        if (metric.value >= metric.target) return 'value-good'
        if (metric.value >= metric.target * 0.8) return 'value-warning'
        return 'value-danger'
      }
    },
    
    getTrendClass(trend, isCost = false) {
      if (trend === 0) return 'trend-neutral'
      
      if (isCost) {
        // 对于成本指标，趋势下降是好的
        return trend < 0 ? 'trend-good' : 'trend-bad'
      } else {
        // 对于效率指标，趋势上升是好的
        return trend > 0 ? 'trend-good' : 'trend-bad'
      }
    },
    
    getProgressWidth(metric, isCost = false) {
      if (isCost) {
        // 对于成本指标，值越低越好
        const ratio = metric.target / Math.max(metric.value, metric.target)
        return `${Math.min(ratio * 100, 100)}%`
      } else {
        // 对于效率指标，值越高越好
        const ratio = metric.value / Math.max(metric.value, metric.target)
        return `${Math.min(ratio * 100, 100)}%`
      }
    },
    
    getActiveChartTitle() {
      const chart = this.chartOptions.find(c => c.id === this.activeChart)
      return chart ? chart.name : ''
    },
    
    getMockChartData() {
      // 生成模拟图表数据
      const data = []
      let color = '#3498db'
      
      switch (this.activeChart) {
        case 'pd_trend':
          color = '#3498db'
          data.push(
            { height: '40%', color },
            { height: '45%', color },
            { height: '55%', color },
            { height: '60%', color },
            { height: '70%', color },
            { height: '85%', color }
          )
          break
        case 'cost_trend':
          color = '#e74c3c'
          data.push(
            { height: '80%', color },
            { height: '75%', color },
            { height: '65%', color },
            { height: '55%', color },
            { height: '40%', color },
            { height: '25%', color }
          )
          break
        case 'build_time':
          color = '#2ecc71'
          data.push(
            { height: '90%', color },
            { height: '80%', color },
            { height: '70%', color },
            { height: '60%', color },
            { height: '50%', color },
            { height: '35%', color }
          )
          break
        case 'issue_rate':
          color = '#f39c12'
          data.push(
            { height: '75%', color },
            { height: '65%', color },
            { height: '70%', color },
            { height: '60%', color },
            { height: '50%', color },
            { height: '45%', color }
          )
          break
        default:
          data.push(
            { height: '50%', color },
            { height: '60%', color },
            { height: '70%', color },
            { height: '65%', color },
            { height: '75%', color },
            { height: '80%', color }
          )
      }
      
      return data
    }
  }
}
</script>

<style scoped>
.efficiency-metrics {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.efficiency-metrics h2 {
  color: #2c3e50;
  margin-bottom: 30px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.efficiency-metrics h3 {
  color: #34495e;
  margin-bottom: 20px;
}

/* 指标选择器样式 */
.metrics-selector {
  margin-bottom: 30px;
}

.selector-tabs {
  display: flex;
  gap: 10px;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 10px;
}

.tab-btn {
  background: #f5f5f5;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.tab-btn.active {
  background: #3498db;
  color: white;
}

.tab-btn:hover:not(.active) {
  background: #e0e0e0;
}

/* 指标卡片样式 */
.metrics-grid {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.metric-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.metric-card.level1 {
  border-left: 4px solid #3498db;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.metric-header h3,
.metric-header h4 {
  margin: 0;
  color: #2c3e50;
}

.metric-value {
  font-size: 1.5em;
  font-weight: 700;
  display: flex;
  align-items: center;
}

.metric-unit {
  font-size: 0.6em;
  margin-left: 5px;
  opacity: 0.7;
}

.metric-trend {
  font-size: 0.6em;
  margin-left: 10px;
  padding: 3px 6px;
  border-radius: 4px;
}

.value-good {
  color: #2ecc71;
}

.value-warning {
  color: #f39c12;
}

.value-danger {
  color: #e74c3c;
}

.trend-good {
  background: rgba(46, 204, 113, 0.2);
  color: #27ae60;
}

.trend-bad {
  background: rgba(231, 76, 60, 0.2);
  color: #c0392b;
}

.trend-neutral {
  background: rgba(149, 165, 166, 0.2);
  color: #7f8c8d;
}

.metric-info {
  margin-bottom: 15px;
}

.metric-desc {
  color: #34495e;
  margin-bottom: 5px;
}

.metric-source {
  color: #7f8c8d;
  font-size: 0.9em;
}

.metric-formula {
  color: #34495e;
  margin-bottom: 15px;
  font-size: 0.9em;
}

.formula-label {
  font-weight: 500;
  color: #2c3e50;
}

/* 目标进度条样式 */
.metric-target {
  margin-top: 15px;
}

.target-bar {
  height: 6px;
  background: #ecf0f1;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 5px;
}

.target-progress {
  height: 100%;
  background: #3498db;
  border-radius: 3px;
}

.target-text {
  color: #7f8c8d;
  font-size: 0.9em;
}

/* 二级指标样式 */
.level2-section {
  margin-top: 20px;
}

.level2-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.metric-card.level2 {
  border-left: 4px solid #2ecc71;
}

/* 趋势图表样式 */
.trend-charts {
  margin-top: 40px;
}

.chart-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.chart-tab {
  background: #f5f5f5;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.chart-tab.active {
  background: #3498db;
  color: white;
}

.chart-tab:hover:not(.active) {
  background: #e0e0e0;
}

.chart-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* 模拟图表样式 */
.chart-placeholder {
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-mock {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-title {
  text-align: center;
  margin-bottom: 20px;
  color: #2c3e50;
  font-weight: 500;
}

.chart-mock-content {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  padding: 0 20px;
  border-bottom: 1px solid #e0e0e0;
}

.mock-bar {
  width: 40px;
  border-radius: 4px 4px 0 0;
  transition: height 0.5s ease;
}

.chart-mock-labels {
  display: flex;
  justify-content: space-around;
  padding: 10px 20px;
}

.chart-mock-labels span {
  color: #7f8c8d;
  font-size: 0.9em;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .level2-grid {
    grid-template-columns: 1fr;
  }
  
  .metric-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .metric-value {
    margin-top: 10px;
  }
  
  .chart-mock-content {
    padding: 0 10px;
  }
  
  .mock-bar {
    width: 30px;
  }
}
</style>