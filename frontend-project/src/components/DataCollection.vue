<template>
  <div class="data-collection">
    <h2>数据采集与上报</h2>
    
    <!-- 场景创建表单 -->
    <div class="scenario-creation">
      <h3>场景创建</h3>
      <div class="form-card">
        <form @submit.prevent="createScenario">
          <div class="form-group">
            <label for="scenarioId">场景唯一标识</label>
            <input 
              type="text" 
              id="scenarioId" 
              v-model="scenarioForm.scenario_id" 
              placeholder="例如: proj_abc"
              required
            >
          </div>
          
          <div class="form-group">
            <label for="scenarioName">场景名称</label>
            <input 
              type="text" 
              id="scenarioName" 
              v-model="scenarioForm.scenario_name" 
              placeholder="例如: 组件库优化"
              required
            >
          </div>
          
          <div class="form-group">
            <label for="scenarioOwner">场景负责人</label>
            <input 
              type="text" 
              id="scenarioOwner" 
              v-model="scenarioForm.scenario_owner" 
              placeholder="例如: 李四"
              required
            >
          </div>
          
          <div class="form-group">
            <label for="scenarioDesc">场景描述</label>
            <textarea 
              id="scenarioDesc" 
              v-model="scenarioForm.scenario_desc" 
              placeholder="例如: 组件库性能与复用优化"
              rows="3"
            ></textarea>
          </div>
          
          <button type="submit" class="btn-primary">创建场景</button>
        </form>
      </div>
    </div>
    
    <!-- 阶段耗时采集 -->
    <div class="stage-collection">
      <h3>阶段耗时采集</h3>
      <div class="form-card">
        <form @submit.prevent="submitStageData">
          <div class="form-row">
            <div class="form-group">
              <label for="executionId">执行记录ID</label>
              <input 
                type="text" 
                id="executionId" 
                v-model="stageForm.execution_id" 
                placeholder="例如: build_20240601_001"
                required
              >
            </div>
            
            <div class="form-group">
              <label for="relatedScenario">关联场景</label>
              <select id="relatedScenario" v-model="stageForm.scenario_id" required>
                <option value="">请选择场景</option>
                <option 
                  v-for="scenario in scenarios" 
                  :key="scenario.scenario_id" 
                  :value="scenario.scenario_id"
                >
                  {{ scenario.scenario_name }}
                </option>
              </select>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="startTime">开始时间</label>
              <input 
                type="datetime-local" 
                id="startTime" 
                v-model="stageForm.started_at" 
                required
              >
            </div>
            
            <div class="form-group">
              <label for="endTime">结束时间</label>
              <input 
                type="datetime-local" 
                id="endTime" 
                v-model="stageForm.ended_at" 
                required
              >
            </div>
          </div>
          
          <!-- 步骤耗时明细 -->
          <div class="steps-section">
            <label>步骤耗时明细（分钟）</label>
            <div class="steps-grid">
              <div 
                v-for="(step, index) in stageForm.step_durations" 
                :key="index" 
                class="step-item"
              >
                <input 
                  type="text" 
                  v-model="step.name" 
                  placeholder="步骤名称"
                  class="step-name"
                >
                <input 
                  type="number" 
                  v-model.number="step.duration" 
                  placeholder="耗时"
                  step="0.1"
                  min="0"
                  class="step-duration"
                >
                <button 
                  type="button" 
                  @click="removeStep(index)" 
                  class="btn-remove"
                  v-if="stageForm.step_durations.length > 1"
                >
                  ×
                </button>
              </div>
            </div>
            <button type="button" @click="addStep" class="btn-secondary">添加步骤</button>
          </div>
          
          <div class="form-group">
            <label>总耗时（自动计算）</label>
            <div class="calculated-value">{{ totalDuration.toFixed(1) }} 分钟</div>
          </div>
          
          <button type="submit" class="btn-primary">提交阶段数据</button>
        </form>
      </div>
    </div>
    
    <!-- 数据预览 -->
    <div class="data-preview">
      <h3>数据预览</h3>
      
      <!-- 场景列表 -->
      <div class="preview-section">
        <h4>已创建场景</h4>
        <div class="scenario-list">
          <div 
            v-for="scenario in scenarios" 
            :key="scenario.scenario_id" 
            class="scenario-item"
          >
            <div class="scenario-info">
              <strong>{{ scenario.scenario_name }}</strong>
              <span class="scenario-id">ID: {{ scenario.scenario_id }}</span>
              <span class="scenario-owner">负责人: {{ scenario.scenario_owner }}</span>
              <p class="scenario-desc">{{ scenario.scenario_desc }}</p>
            </div>
            <div class="scenario-meta">
              <span class="created-time">{{ formatTime(scenario.created_at) }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 阶段数据列表 -->
      <div class="preview-section">
        <h4>阶段耗时记录</h4>
        <div class="stage-list">
          <div 
            v-for="stage in stageRecords" 
            :key="stage.execution_id" 
            class="stage-item"
          >
            <div class="stage-header">
              <strong>{{ stage.execution_id }}</strong>
              <span class="stage-scenario">场景: {{ getScenarioName(stage.scenario_id) }}</span>
              <span class="stage-duration">总耗时: {{ stage.total_duration_minutes }}分钟</span>
            </div>
            <div class="stage-steps">
              <span 
                v-for="(duration, stepName) in stage.step_durations" 
                :key="stepName" 
                class="step-tag"
              >
                {{ stepName }}: {{ duration }}分钟
              </span>
            </div>
            <div class="stage-time">
              {{ formatTime(stage.started_at) }} - {{ formatTime(stage.ended_at) }}
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- JSON数据展示 -->
    <div class="json-preview">
      <h3>JSON数据格式</h3>
      <div class="json-tabs">
        <button 
          :class="['tab-btn', { active: activeTab === 'scenario' }]" 
          @click="activeTab = 'scenario'"
        >
          场景创建
        </button>
        <button 
          :class="['tab-btn', { active: activeTab === 'stage' }]" 
          @click="activeTab = 'stage'"
        >
          阶段耗时
        </button>
      </div>
      
      <div class="json-content">
        <pre v-if="activeTab === 'scenario'">{{ scenarioJsonExample }}</pre>
        <pre v-if="activeTab === 'stage'">{{ stageJsonExample }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DataCollection',
  data() {
    return {
      activeTab: 'scenario',
      
      // 场景创建表单
      scenarioForm: {
        scenario_id: '',
        scenario_name: '',
        scenario_owner: '',
        scenario_desc: ''
      },
      
      // 阶段耗时表单
      stageForm: {
        execution_id: '',
        scenario_id: '',
        started_at: '',
        ended_at: '',
        step_durations: [
          { name: '依赖安装', duration: 0 },
          { name: '编译', duration: 0 },
          { name: '打包', duration: 0 }
        ]
      },
      
      // 数据存储
      scenarios: [
        {
          scenario_id: 'proj_abc',
          scenario_name: '组件库优化',
          scenario_owner: '李四',
          created_at: '2024-06-01T09:00:00Z',
          scenario_desc: '组件库性能与复用优化'
        }
      ],
      
      stageRecords: [
        {
          execution_id: 'build_20240601_001',
          scenario_id: 'proj_abc',
          started_at: '2024-06-01T10:00:00Z',
          ended_at: '2024-06-01T10:05:00Z',
          step_durations: {
            '依赖安装': 1.5,
            '编译': 2.0,
            '打包': 1.5
          },
          total_duration_minutes: 5.0
        }
      ]
    }
  },
  
  computed: {
    totalDuration() {
      return this.stageForm.step_durations.reduce((total, step) => {
        return total + (step.duration || 0)
      }, 0)
    },
    
    scenarioJsonExample() {
      return JSON.stringify({
        scenario_id: 'proj_abc',
        scenario_name: '组件库优化',
        scenario_owner: '李四',
        created_at: '2024-06-01T09:00:00Z',
        scenario_desc: '组件库性能与复用优化'
      }, null, 2)
    },
    
    stageJsonExample() {
      return JSON.stringify({
        execution_id: 'build_20240601_001',
        scenario_id: 'proj_abc',
        started_at: '2024-06-01T10:00:00Z',
        ended_at: '2024-06-01T10:05:00Z',
        step_durations: {
          '依赖安装': 1.5,
          '编译': 2.0,
          '打包': 1.5
        },
        total_duration_minutes: 5.0
      }, null, 2)
    }
  },
  
  methods: {
    createScenario() {
      const newScenario = {
        ...this.scenarioForm,
        created_at: new Date().toISOString()
      }
      
      this.scenarios.push(newScenario)
      
      // 重置表单
      this.scenarioForm = {
        scenario_id: '',
        scenario_name: '',
        scenario_owner: '',
        scenario_desc: ''
      }
      
      alert('场景创建成功！')
    },
    
    submitStageData() {
      // 转换步骤数据格式
      const stepDurations = {}
      this.stageForm.step_durations.forEach(step => {
        if (step.name && step.duration) {
          stepDurations[step.name] = step.duration
        }
      })
      
      const newStageRecord = {
        execution_id: this.stageForm.execution_id,
        scenario_id: this.stageForm.scenario_id,
        started_at: this.stageForm.started_at,
        ended_at: this.stageForm.ended_at,
        step_durations: stepDurations,
        total_duration_minutes: this.totalDuration
      }
      
      this.stageRecords.push(newStageRecord)
      
      // 重置表单
      this.stageForm = {
        execution_id: '',
        scenario_id: '',
        started_at: '',
        ended_at: '',
        step_durations: [
          { name: '依赖安装', duration: 0 },
          { name: '编译', duration: 0 },
          { name: '打包', duration: 0 }
        ]
      }
      
      alert('阶段数据提交成功！')
    },
    
    addStep() {
      this.stageForm.step_durations.push({ name: '', duration: 0 })
    },
    
    removeStep(index) {
      this.stageForm.step_durations.splice(index, 1)
    },
    
    getScenarioName(scenarioId) {
      const scenario = this.scenarios.find(s => s.scenario_id === scenarioId)
      return scenario ? scenario.scenario_name : scenarioId
    },
    
    formatTime(timeString) {
      return new Date(timeString).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.data-collection {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.data-collection h2 {
  color: #2c3e50;
  margin-bottom: 30px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.data-collection h3 {
  color: #34495e;
  margin-bottom: 20px;
}

.data-collection h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.1em;
}

/* 表单样式 */
.form-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3498db;
}

/* 步骤耗时样式 */
.steps-section {
  margin-bottom: 20px;
}

.steps-section label {
  display: block;
  margin-bottom: 12px;
  font-weight: 500;
  color: #2c3e50;
}

.steps-grid {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.step-item {
  display: flex;
  gap: 10px;
  align-items: center;
}

.step-name {
  flex: 2;
}

.step-duration {
  flex: 1;
}

.btn-remove {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  width: 30px;
  height: 30px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-remove:hover {
  background: #c0392b;
}

.calculated-value {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  font-weight: 500;
  color: #2c3e50;
}

/* 按钮样式 */
.btn-primary {
  background: #3498db;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.3s;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

/* 预览区域样式 */
.data-preview {
  margin-top: 40px;
}

.preview-section {
  margin-bottom: 30px;
}

.scenario-list,
.stage-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.scenario-item,
.stage-item {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #3498db;
}

.scenario-info {
  margin-bottom: 10px;
}

.scenario-info strong {
  color: #2c3e50;
  font-size: 1.1em;
}

.scenario-id,
.scenario-owner {
  display: inline-block;
  margin-left: 15px;
  color: #7f8c8d;
  font-size: 0.9em;
}

.scenario-desc {
  margin: 8px 0 0 0;
  color: #34495e;
}

.scenario-meta,
.stage-time {
  color: #95a5a6;
  font-size: 0.9em;
}

.stage-header {
  margin-bottom: 10px;
}

.stage-header strong {
  color: #2c3e50;
  margin-right: 15px;
}

.stage-scenario,
.stage-duration {
  color: #7f8c8d;
  font-size: 0.9em;
  margin-right: 15px;
}

.stage-steps {
  margin-bottom: 10px;
}

.step-tag {
  display: inline-block;
  background: #ecf0f1;
  color: #2c3e50;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8em;
  margin-right: 8px;
  margin-bottom: 4px;
}

/* JSON预览样式 */
.json-preview {
  margin-top: 40px;
}

.json-tabs {
  display: flex;
  margin-bottom: 20px;
}

.tab-btn {
  background: #ecf0f1;
  border: none;
  padding: 10px 20px;
  cursor: pointer;
  border-radius: 4px 4px 0 0;
  margin-right: 2px;
  transition: background 0.3s;
}

.tab-btn.active {
  background: #3498db;
  color: white;
}

.tab-btn:hover:not(.active) {
  background: #bdc3c7;
}

.json-content {
  background: #2c3e50;
  color: #ecf0f1;
  padding: 20px;
  border-radius: 0 4px 4px 4px;
  overflow-x: auto;
}

.json-content pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .step-item {
    flex-direction: column;
    align-items: stretch;
  }
  
  .step-name,
  .step-duration {
    flex: none;
  }
  
  .scenario-id,
  .scenario-owner {
    display: block;
    margin-left: 0;
    margin-top: 5px;
  }
}
</style>