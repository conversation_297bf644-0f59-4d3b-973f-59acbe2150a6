#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/dev/frontend-project/node_modules/.pnpm/js-yaml@3.14.1/node_modules/js-yaml/bin/node_modules:/Users/<USER>/Desktop/dev/frontend-project/node_modules/.pnpm/js-yaml@3.14.1/node_modules/js-yaml/node_modules:/Users/<USER>/Desktop/dev/frontend-project/node_modules/.pnpm/js-yaml@3.14.1/node_modules:/Users/<USER>/Desktop/dev/frontend-project/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/dev/frontend-project/node_modules/.pnpm/js-yaml@3.14.1/node_modules/js-yaml/bin/node_modules:/Users/<USER>/Desktop/dev/frontend-project/node_modules/.pnpm/js-yaml@3.14.1/node_modules/js-yaml/node_modules:/Users/<USER>/Desktop/dev/frontend-project/node_modules/.pnpm/js-yaml@3.14.1/node_modules:/Users/<USER>/Desktop/dev/frontend-project/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/js-yaml.js" "$@"
else
  exec node  "$basedir/../../bin/js-yaml.js" "$@"
fi
