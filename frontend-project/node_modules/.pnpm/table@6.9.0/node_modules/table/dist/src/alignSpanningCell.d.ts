import type { SpanningCellContext } from './spanningCellManager';
import type { RangeConfig } from './types/internal';
/**
 * Fill content into all cells in range in order to calculate total height
 */
export declare const wrapRangeContent: (rangeConfig: RangeConfig, rangeWidth: number, context: SpanningCellContext) => string[];
export declare const alignVerticalRangeContent: (range: RangeConfig, content: string[], context: SpanningCellContext) => string[];
