import type { SpanningCellManager } from './spanningCellManager';
import type { DrawVerticalLine } from './types/api';
import type { BodyBorderConfig, Row } from './types/internal';
export declare type DrawRowConfig = {
    border: BodyBorderConfig;
    drawVerticalLine: DrawVerticalLine;
    spanningCellManager?: SpanningCellManager;
    rowIndex?: number;
};
export declare const drawRow: (row: Row, config: DrawRowConfig) => string;
