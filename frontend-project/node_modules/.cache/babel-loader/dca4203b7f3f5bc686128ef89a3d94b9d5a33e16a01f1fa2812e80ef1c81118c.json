{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, normalizeClass as _normalizeClass, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeStyle as _normalizeStyle } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-header\"\n};\nconst _hoisted_3 = {\n  class: \"main-nav\"\n};\nconst _hoisted_4 = {\n  class: \"overview-section\"\n};\nconst _hoisted_5 = {\n  class: \"metric-cards\"\n};\nconst _hoisted_6 = {\n  class: \"metric-card efficiency\"\n};\nconst _hoisted_7 = {\n  class: \"metric-content\"\n};\nconst _hoisted_8 = {\n  class: \"metric-value\"\n};\nconst _hoisted_9 = {\n  class: \"metric-trend positive\"\n};\nconst _hoisted_10 = {\n  class: \"metric-card cost\"\n};\nconst _hoisted_11 = {\n  class: \"metric-content\"\n};\nconst _hoisted_12 = {\n  class: \"metric-value\"\n};\nconst _hoisted_13 = {\n  class: \"metric-trend positive\"\n};\nconst _hoisted_14 = {\n  class: \"efficiency-section\"\n};\nconst _hoisted_15 = {\n  class: \"level-one-metrics\"\n};\nconst _hoisted_16 = {\n  class: \"metrics-grid\"\n};\nconst _hoisted_17 = {\n  class: \"metric-header\"\n};\nconst _hoisted_18 = {\n  class: \"metric-name\"\n};\nconst _hoisted_19 = {\n  class: \"metric-target\"\n};\nconst _hoisted_20 = {\n  class: \"metric-progress\"\n};\nconst _hoisted_21 = {\n  class: \"progress-bar\"\n};\nconst _hoisted_22 = {\n  class: \"progress-text\"\n};\nconst _hoisted_23 = {\n  class: \"metric-details\"\n};\nconst _hoisted_24 = {\n  class: \"data-source\"\n};\nconst _hoisted_25 = {\n  class: \"level-two-metrics\"\n};\nconst _hoisted_26 = {\n  class: \"metric-category\"\n};\nconst _hoisted_27 = {\n  class: \"metrics-table\"\n};\nconst _hoisted_28 = {\n  class: \"metric-category\"\n};\nconst _hoisted_29 = {\n  class: \"metrics-table\"\n};\nconst _hoisted_30 = {\n  class: \"metric-category\"\n};\nconst _hoisted_31 = {\n  class: \"metrics-table\"\n};\nconst _hoisted_32 = {\n  class: \"cost-section\"\n};\nconst _hoisted_33 = {\n  class: \"cost-overview\"\n};\nconst _hoisted_34 = {\n  class: \"cost-metrics-grid\"\n};\nconst _hoisted_35 = {\n  class: \"cost-metric-header\"\n};\nconst _hoisted_36 = {\n  class: \"cost-target\"\n};\nconst _hoisted_37 = {\n  class: \"cost-metric-body\"\n};\nconst _hoisted_38 = {\n  class: \"cost-value\"\n};\nconst _hoisted_39 = {\n  class: \"current-value\"\n};\nconst _hoisted_40 = {\n  class: \"cost-progress\"\n};\nconst _hoisted_41 = {\n  class: \"progress-bar\"\n};\nconst _hoisted_42 = {\n  class: \"cost-source\"\n};\nconst _hoisted_43 = {\n  class: \"cost-detail\"\n};\nconst _hoisted_44 = {\n  class: \"cost-detail-table\"\n};\nconst _hoisted_45 = {\n  class: \"key-metrics-section\"\n};\nconst _hoisted_46 = {\n  class: \"key-metrics-grid\"\n};\nconst _hoisted_47 = {\n  class: \"key-metric-content\"\n};\nconst _hoisted_48 = {\n  class: \"key-metric-value\"\n};\nconst _hoisted_49 = {\n  class: \"key-metric-target\"\n};\nconst _hoisted_50 = {\n  class: \"key-metric-source\"\n};\nconst _hoisted_51 = {\n  class: \"data-collection-section\"\n};\nconst _hoisted_52 = {\n  class: \"collection-status\"\n};\nconst _hoisted_53 = {\n  class: \"source-name\"\n};\nconst _hoisted_54 = {\n  class: \"last-update\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"header\", _hoisted_2, [_cache[2] || (_cache[2] = _createElementVNode(\"h1\", null, \"前端基建效率与成本洞察平台\", -1 /* CACHED */)), _cache[3] || (_cache[3] = _createElementVNode(\"p\", {\n    class: \"subtitle\"\n  }, \"量化前端基建效率提升与成本节约\", -1 /* CACHED */)), _createElementVNode(\"nav\", _hoisted_3, [_createVNode(_component_router_link, {\n    to: \"/\",\n    class: _normalizeClass([\"nav-link\", {\n      active: _ctx.$route.path === '/'\n    }])\n  }, {\n    default: _withCtx(() => _cache[0] || (_cache[0] = [_createTextVNode(\"统计概览\")])),\n    _: 1 /* STABLE */,\n    __: [0]\n  }, 8 /* PROPS */, [\"class\"]), _createVNode(_component_router_link, {\n    to: \"/data-collection\",\n    class: _normalizeClass([\"nav-link\", {\n      active: _ctx.$route.path === '/data-collection'\n    }])\n  }, {\n    default: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\"数据采集\")])),\n    _: 1 /* STABLE */,\n    __: [1]\n  }, 8 /* PROPS */, [\"class\"])])]), _createCommentVNode(\" 北极星指标概览 \"), _createElementVNode(\"div\", _hoisted_4, [_cache[8] || (_cache[8] = _createElementVNode(\"h2\", null, \"北极星指标\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n    class: \"metric-icon\"\n  }, \"⚡\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_7, [_cache[4] || (_cache[4] = _createElementVNode(\"h3\", null, \"累计节约人天数\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_8, _toDisplayString($data.totalPDSaved) + \" PD\", 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_9, \"+\" + _toDisplayString($data.monthlyPDSaved) + \" PD/月\", 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_10, [_cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n    class: \"metric-icon\"\n  }, \"💰\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_11, [_cache[6] || (_cache[6] = _createElementVNode(\"h3\", null, \"累计成本节约\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_12, \"¥\" + _toDisplayString($data.totalCostSaved.toLocaleString()), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_13, \"+¥\" + _toDisplayString($data.monthlyCostSaved.toLocaleString()) + \"/月\", 1 /* TEXT */)])])])]), _createCommentVNode(\" 提效维度统计 \"), _createElementVNode(\"div\", _hoisted_14, [_cache[17] || (_cache[17] = _createElementVNode(\"h2\", null, \"提效维度统计\", -1 /* CACHED */)), _createCommentVNode(\" 一级过程指标 \"), _createElementVNode(\"div\", _hoisted_15, [_cache[9] || (_cache[9] = _createElementVNode(\"h3\", null, \"一级过程指标\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_16, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.efficiencyL1Metrics, metric => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"metric-item\",\n      key: metric.name\n    }, [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"span\", _hoisted_18, _toDisplayString(metric.name), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_19, \"目标: \" + _toDisplayString(metric.target), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", {\n      class: \"progress-fill\",\n      style: _normalizeStyle({\n        width: metric.progress + '%'\n      })\n    }, null, 4 /* STYLE */)]), _createElementVNode(\"span\", _hoisted_22, _toDisplayString(metric.current) + \" / \" + _toDisplayString(metric.target), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"span\", _hoisted_24, \"数据来源: \" + _toDisplayString(metric.dataSource), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 二级过程指标 \"), _createElementVNode(\"div\", _hoisted_25, [_cache[16] || (_cache[16] = _createElementVNode(\"h3\", null, \"二级过程指标\", -1 /* CACHED */)), _createCommentVNode(\" 工程构建类 \"), _createElementVNode(\"div\", _hoisted_26, [_cache[11] || (_cache[11] = _createElementVNode(\"h4\", null, \"工程构建类\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"table\", null, [_cache[10] || (_cache[10] = _createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, \"指标名称\"), _createElementVNode(\"th\", null, \"当前值\"), _createElementVNode(\"th\", null, \"目标值\"), _createElementVNode(\"th\", null, \"完成率\"), _createElementVNode(\"th\", null, \"数据来源\")])], -1 /* CACHED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.buildMetrics, metric => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: metric.name\n    }, [_createElementVNode(\"td\", null, _toDisplayString(metric.name), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(metric.current), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(metric.target), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"span\", {\n      class: _normalizeClass(['completion-rate', metric.status])\n    }, _toDisplayString(metric.completionRate) + \"% \", 3 /* TEXT, CLASS */)]), _createElementVNode(\"td\", null, _toDisplayString(metric.dataSource), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))])])])]), _createCommentVNode(\" 线上问题处理类 \"), _createElementVNode(\"div\", _hoisted_28, [_cache[13] || (_cache[13] = _createElementVNode(\"h4\", null, \"线上问题处理类\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"table\", null, [_cache[12] || (_cache[12] = _createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, \"指标名称\"), _createElementVNode(\"th\", null, \"当前值\"), _createElementVNode(\"th\", null, \"目标值\"), _createElementVNode(\"th\", null, \"完成率\"), _createElementVNode(\"th\", null, \"数据来源\")])], -1 /* CACHED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.problemMetrics, metric => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: metric.name\n    }, [_createElementVNode(\"td\", null, _toDisplayString(metric.name), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(metric.current), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(metric.target), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"span\", {\n      class: _normalizeClass(['completion-rate', metric.status])\n    }, _toDisplayString(metric.completionRate) + \"% \", 3 /* TEXT, CLASS */)]), _createElementVNode(\"td\", null, _toDisplayString(metric.dataSource), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))])])])]), _createCommentVNode(\" 组件复用类 \"), _createElementVNode(\"div\", _hoisted_30, [_cache[15] || (_cache[15] = _createElementVNode(\"h4\", null, \"组件复用类\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"table\", null, [_cache[14] || (_cache[14] = _createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, \"指标名称\"), _createElementVNode(\"th\", null, \"当前值\"), _createElementVNode(\"th\", null, \"目标值\"), _createElementVNode(\"th\", null, \"完成率\"), _createElementVNode(\"th\", null, \"数据来源\")])], -1 /* CACHED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.componentMetrics, metric => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: metric.name\n    }, [_createElementVNode(\"td\", null, _toDisplayString(metric.name), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(metric.current), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(metric.target), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"span\", {\n      class: _normalizeClass(['completion-rate', metric.status])\n    }, _toDisplayString(metric.completionRate) + \"% \", 3 /* TEXT, CLASS */)]), _createElementVNode(\"td\", null, _toDisplayString(metric.dataSource), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))])])])])])]), _createCommentVNode(\" 成本维度统计 \"), _createElementVNode(\"div\", _hoisted_32, [_cache[21] || (_cache[21] = _createElementVNode(\"h2\", null, \"成本维度统计\", -1 /* CACHED */)), _createCommentVNode(\" 一级过程指标 \"), _createElementVNode(\"div\", _hoisted_33, [_cache[18] || (_cache[18] = _createElementVNode(\"h3\", null, \"一级过程指标\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_34, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.costL1Metrics, metric => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"cost-metric-card\",\n      key: metric.name\n    }, [_createElementVNode(\"div\", _hoisted_35, [_createElementVNode(\"h4\", null, _toDisplayString(metric.name), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_36, \"目标: \" + _toDisplayString(metric.target), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"span\", _hoisted_39, \"¥\" + _toDisplayString(metric.currentSaving.toLocaleString()), 1 /* TEXT */), _createElementVNode(\"span\", {\n      class: _normalizeClass([\"optimization-rate\", metric.status])\n    }, _toDisplayString(metric.optimizationRate) + \"% \", 3 /* TEXT, CLASS */)]), _createElementVNode(\"div\", _hoisted_40, [_createElementVNode(\"div\", _hoisted_41, [_createElementVNode(\"div\", {\n      class: \"progress-fill\",\n      style: _normalizeStyle({\n        width: metric.progress + '%'\n      })\n    }, null, 4 /* STYLE */)])]), _createElementVNode(\"div\", _hoisted_42, _toDisplayString(metric.dataSource), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 二级过程指标 \"), _createElementVNode(\"div\", _hoisted_43, [_cache[20] || (_cache[20] = _createElementVNode(\"h3\", null, \"二级过程指标\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_44, [_createElementVNode(\"table\", null, [_cache[19] || (_cache[19] = _createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, \"指标名称\"), _createElementVNode(\"th\", null, \"当前节约金额\"), _createElementVNode(\"th\", null, \"目标值\"), _createElementVNode(\"th\", null, \"完成率\"), _createElementVNode(\"th\", null, \"数据来源\"), _createElementVNode(\"th\", null, \"趋势\")])], -1 /* CACHED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.costL2Metrics, metric => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: metric.name\n    }, [_createElementVNode(\"td\", null, _toDisplayString(metric.name), 1 /* TEXT */), _createElementVNode(\"td\", null, \"¥\" + _toDisplayString(metric.currentSaving.toLocaleString()), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(metric.target), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"span\", {\n      class: _normalizeClass(['completion-rate', metric.status])\n    }, _toDisplayString(metric.completionRate) + \"% \", 3 /* TEXT, CLASS */)]), _createElementVNode(\"td\", null, _toDisplayString(metric.dataSource), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"span\", {\n      class: _normalizeClass(['trend', metric.trend])\n    }, _toDisplayString(metric.trendText), 3 /* TEXT, CLASS */)])]);\n  }), 128 /* KEYED_FRAGMENT */))])])])])]), _createCommentVNode(\" 重点执行指标 \"), _createElementVNode(\"div\", _hoisted_45, [_cache[23] || (_cache[23] = _createElementVNode(\"h2\", null, \"重点执行指标\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_46, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.keyMetrics, metric => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"key-metric-card\",\n      key: metric.name\n    }, [_cache[22] || (_cache[22] = _createElementVNode(\"div\", {\n      class: \"key-metric-icon\"\n    }, \"📊\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_47, [_createElementVNode(\"h4\", null, _toDisplayString(metric.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_48, _toDisplayString(metric.current), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_49, \"目标: \" + _toDisplayString(metric.target), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_50, _toDisplayString(metric.dataSource), 1 /* TEXT */)]), _createElementVNode(\"div\", {\n      class: _normalizeClass([\"key-metric-status\", metric.status])\n    }, _toDisplayString(metric.statusText), 3 /* TEXT, CLASS */)]);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 数据采集状态 \"), _createElementVNode(\"div\", _hoisted_51, [_cache[24] || (_cache[24] = _createElementVNode(\"h2\", null, \"数据采集状态\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_52, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.dataSources, source => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"status-item\",\n      key: source.name\n    }, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"status-indicator\", source.status])\n    }, null, 2 /* CLASS */), _createElementVNode(\"span\", _hoisted_53, _toDisplayString(source.name), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_54, \"最后更新: \" + _toDisplayString(source.lastUpdate), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_router_link", "to", "_normalizeClass", "active", "_ctx", "$route", "path", "_cache", "_createCommentVNode", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_toDisplayString", "$data", "totalPDSaved", "_hoisted_9", "monthlyPDSaved", "_hoisted_10", "_hoisted_11", "_hoisted_12", "totalCostSaved", "toLocaleString", "_hoisted_13", "monthlyCostSaved", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_Fragment", "_renderList", "efficiencyL1Metrics", "metric", "key", "name", "_hoisted_17", "_hoisted_18", "_hoisted_19", "target", "_hoisted_20", "_hoisted_21", "style", "_normalizeStyle", "width", "progress", "_hoisted_22", "current", "_hoisted_23", "_hoisted_24", "dataSource", "_hoisted_25", "_hoisted_26", "_hoisted_27", "buildMetrics", "status", "completionRate", "_hoisted_28", "_hoisted_29", "problemMetrics", "_hoisted_30", "_hoisted_31", "componentMetrics", "_hoisted_32", "_hoisted_33", "_hoisted_34", "costL1Metrics", "_hoisted_35", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "currentSaving", "optimizationRate", "_hoisted_40", "_hoisted_41", "_hoisted_42", "_hoisted_43", "_hoisted_44", "costL2Metrics", "trend", "trendText", "_hoisted_45", "_hoisted_46", "keyMetrics", "_hoisted_47", "_hoisted_48", "_hoisted_49", "_hoisted_50", "statusText", "_hoisted_51", "_hoisted_52", "dataSources", "source", "_hoisted_53", "_hoisted_54", "lastUpdate"], "sources": ["/Users/<USER>/Desktop/dev/frontend-project/src/views/Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard\">\n    <header class=\"dashboard-header\">\n      <h1>前端基建效率与成本洞察平台</h1>\n      <p class=\"subtitle\">量化前端基建效率提升与成本节约</p>\n      <nav class=\"main-nav\">\n        <router-link to=\"/\" class=\"nav-link\" :class=\"{ active: $route.path === '/' }\">统计概览</router-link>\n        <router-link to=\"/data-collection\" class=\"nav-link\" :class=\"{ active: $route.path === '/data-collection' }\">数据采集</router-link>\n      </nav>\n    </header>\n\n    <!-- 北极星指标概览 -->\n    <div class=\"overview-section\">\n      <h2>北极星指标</h2>\n      <div class=\"metric-cards\">\n        <div class=\"metric-card efficiency\">\n          <div class=\"metric-icon\">⚡</div>\n          <div class=\"metric-content\">\n            <h3>累计节约人天数</h3>\n            <div class=\"metric-value\">{{ totalPDSaved }} PD</div>\n            <div class=\"metric-trend positive\">+{{ monthlyPDSaved }} PD/月</div>\n          </div>\n        </div>\n        <div class=\"metric-card cost\">\n          <div class=\"metric-icon\">💰</div>\n          <div class=\"metric-content\">\n            <h3>累计成本节约</h3>\n            <div class=\"metric-value\">¥{{ totalCostSaved.toLocaleString() }}</div>\n            <div class=\"metric-trend positive\">+¥{{ monthlyCostSaved.toLocaleString() }}/月</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 提效维度统计 -->\n    <div class=\"efficiency-section\">\n      <h2>提效维度统计</h2>\n      \n      <!-- 一级过程指标 -->\n      <div class=\"level-one-metrics\">\n        <h3>一级过程指标</h3>\n        <div class=\"metrics-grid\">\n          <div class=\"metric-item\" v-for=\"metric in efficiencyL1Metrics\" :key=\"metric.name\">\n            <div class=\"metric-header\">\n              <span class=\"metric-name\">{{ metric.name }}</span>\n              <span class=\"metric-target\">目标: {{ metric.target }}</span>\n            </div>\n            <div class=\"metric-progress\">\n              <div class=\"progress-bar\">\n                <div class=\"progress-fill\" :style=\"{width: metric.progress + '%'}\"></div>\n              </div>\n              <span class=\"progress-text\">{{ metric.current }} / {{ metric.target }}</span>\n            </div>\n            <div class=\"metric-details\">\n              <span class=\"data-source\">数据来源: {{ metric.dataSource }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 二级过程指标 -->\n      <div class=\"level-two-metrics\">\n        <h3>二级过程指标</h3>\n        \n        <!-- 工程构建类 -->\n        <div class=\"metric-category\">\n          <h4>工程构建类</h4>\n          <div class=\"metrics-table\">\n            <table>\n              <thead>\n                <tr>\n                  <th>指标名称</th>\n                  <th>当前值</th>\n                  <th>目标值</th>\n                  <th>完成率</th>\n                  <th>数据来源</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr v-for=\"metric in buildMetrics\" :key=\"metric.name\">\n                  <td>{{ metric.name }}</td>\n                  <td>{{ metric.current }}</td>\n                  <td>{{ metric.target }}</td>\n                  <td>\n                    <span :class=\"['completion-rate', metric.status]\">\n                      {{ metric.completionRate }}%\n                    </span>\n                  </td>\n                  <td>{{ metric.dataSource }}</td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        <!-- 线上问题处理类 -->\n        <div class=\"metric-category\">\n          <h4>线上问题处理类</h4>\n          <div class=\"metrics-table\">\n            <table>\n              <thead>\n                <tr>\n                  <th>指标名称</th>\n                  <th>当前值</th>\n                  <th>目标值</th>\n                  <th>完成率</th>\n                  <th>数据来源</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr v-for=\"metric in problemMetrics\" :key=\"metric.name\">\n                  <td>{{ metric.name }}</td>\n                  <td>{{ metric.current }}</td>\n                  <td>{{ metric.target }}</td>\n                  <td>\n                    <span :class=\"['completion-rate', metric.status]\">\n                      {{ metric.completionRate }}%\n                    </span>\n                  </td>\n                  <td>{{ metric.dataSource }}</td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        <!-- 组件复用类 -->\n        <div class=\"metric-category\">\n          <h4>组件复用类</h4>\n          <div class=\"metrics-table\">\n            <table>\n              <thead>\n                <tr>\n                  <th>指标名称</th>\n                  <th>当前值</th>\n                  <th>目标值</th>\n                  <th>完成率</th>\n                  <th>数据来源</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr v-for=\"metric in componentMetrics\" :key=\"metric.name\">\n                  <td>{{ metric.name }}</td>\n                  <td>{{ metric.current }}</td>\n                  <td>{{ metric.target }}</td>\n                  <td>\n                    <span :class=\"['completion-rate', metric.status]\">\n                      {{ metric.completionRate }}%\n                    </span>\n                  </td>\n                  <td>{{ metric.dataSource }}</td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 成本维度统计 -->\n    <div class=\"cost-section\">\n      <h2>成本维度统计</h2>\n      \n      <!-- 一级过程指标 -->\n      <div class=\"cost-overview\">\n        <h3>一级过程指标</h3>\n        <div class=\"cost-metrics-grid\">\n          <div class=\"cost-metric-card\" v-for=\"metric in costL1Metrics\" :key=\"metric.name\">\n            <div class=\"cost-metric-header\">\n              <h4>{{ metric.name }}</h4>\n              <span class=\"cost-target\">目标: {{ metric.target }}</span>\n            </div>\n            <div class=\"cost-metric-body\">\n              <div class=\"cost-value\">\n                <span class=\"current-value\">¥{{ metric.currentSaving.toLocaleString() }}</span>\n                <span class=\"optimization-rate\" :class=\"metric.status\">\n                  {{ metric.optimizationRate }}%\n                </span>\n              </div>\n              <div class=\"cost-progress\">\n                <div class=\"progress-bar\">\n                  <div class=\"progress-fill\" :style=\"{width: metric.progress + '%'}\"></div>\n                </div>\n              </div>\n              <div class=\"cost-source\">{{ metric.dataSource }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 二级过程指标 -->\n      <div class=\"cost-detail\">\n        <h3>二级过程指标</h3>\n        <div class=\"cost-detail-table\">\n          <table>\n            <thead>\n              <tr>\n                <th>指标名称</th>\n                <th>当前节约金额</th>\n                <th>目标值</th>\n                <th>完成率</th>\n                <th>数据来源</th>\n                <th>趋势</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr v-for=\"metric in costL2Metrics\" :key=\"metric.name\">\n                <td>{{ metric.name }}</td>\n                <td>¥{{ metric.currentSaving.toLocaleString() }}</td>\n                <td>{{ metric.target }}</td>\n                <td>\n                  <span :class=\"['completion-rate', metric.status]\">\n                    {{ metric.completionRate }}%\n                  </span>\n                </td>\n                <td>{{ metric.dataSource }}</td>\n                <td>\n                  <span :class=\"['trend', metric.trend]\">\n                    {{ metric.trendText }}\n                  </span>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n\n    <!-- 重点执行指标 -->\n    <div class=\"key-metrics-section\">\n      <h2>重点执行指标</h2>\n      <div class=\"key-metrics-grid\">\n        <div class=\"key-metric-card\" v-for=\"metric in keyMetrics\" :key=\"metric.name\">\n          <div class=\"key-metric-icon\">📊</div>\n          <div class=\"key-metric-content\">\n            <h4>{{ metric.name }}</h4>\n            <div class=\"key-metric-value\">{{ metric.current }}</div>\n            <div class=\"key-metric-target\">目标: {{ metric.target }}</div>\n            <div class=\"key-metric-source\">{{ metric.dataSource }}</div>\n          </div>\n          <div class=\"key-metric-status\" :class=\"metric.status\">\n            {{ metric.statusText }}\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 数据采集状态 -->\n    <div class=\"data-collection-section\">\n      <h2>数据采集状态</h2>\n      <div class=\"collection-status\">\n        <div class=\"status-item\" v-for=\"source in dataSources\" :key=\"source.name\">\n          <div class=\"status-indicator\" :class=\"source.status\"></div>\n          <span class=\"source-name\">{{ source.name }}</span>\n          <span class=\"last-update\">最后更新: {{ source.lastUpdate }}</span>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Dashboard',\n  data() {\n    return {\n      // 北极星指标\n      totalPDSaved: 156.5,\n      monthlyPDSaved: 28.3,\n      totalCostSaved: 1250000,\n      monthlyCostSaved: 185000,\n      \n      // 提效维度一级指标\n      efficiencyL1Metrics: [\n        {\n          name: '构建提效PD',\n          current: '22.5 PD/月',\n          target: '>20 PD/月',\n          progress: 112,\n          dataSource: '构建系统、部署平台'\n        },\n        {\n          name: '问题提效PD',\n          current: '18.2 PD/月',\n          target: '>15 PD/月',\n          progress: 121,\n          dataSource: '监控系统、工单系统'\n        },\n        {\n          name: '组件提效PD',\n          current: '12.8 PD/月',\n          target: '>10 PD/月',\n          progress: 128,\n          dataSource: '代码仓库、组件库'\n        }\n      ],\n      \n      // 工程构建类指标\n      buildMetrics: [\n        {\n          name: '构建速度PD',\n          current: '9.2 PD/月',\n          target: '>8 PD/月',\n          completionRate: 115,\n          status: 'success',\n          dataSource: '构建系统日志'\n        },\n        {\n          name: '部署效率PD',\n          current: '7.8 PD/月',\n          target: '>6 PD/月',\n          completionRate: 130,\n          status: 'success',\n          dataSource: '部署平台日志'\n        },\n        {\n          name: '开发体验PD',\n          current: '5.5 PD/月',\n          target: '>4 PD/月',\n          completionRate: 137,\n          status: 'success',\n          dataSource: 'IDE插件统计'\n        }\n      ],\n      \n      // 线上问题处理类指标\n      problemMetrics: [\n        {\n          name: '发现提效PD',\n          current: '6.2 PD/月',\n          target: '>5 PD/月',\n          completionRate: 124,\n          status: 'success',\n          dataSource: '监控系统'\n        },\n        {\n          name: '定位提效PD',\n          current: '9.5 PD/月',\n          target: '>8 PD/月',\n          completionRate: 118,\n          status: 'success',\n          dataSource: '工单系统'\n        },\n        {\n          name: '解决提效PD',\n          current: '2.5 PD/月',\n          target: '>3 PD/月',\n          completionRate: 83,\n          status: 'warning',\n          dataSource: '工单系统'\n        }\n      ],\n      \n      // 组件复用类指标\n      componentMetrics: [\n        {\n          name: '开发提效PD',\n          current: '4.2 PD/月',\n          target: '>3 PD/月',\n          completionRate: 140,\n          status: 'success',\n          dataSource: '项目管理工具'\n        },\n        {\n          name: '复用节约PD',\n          current: '6.8 PD/月',\n          target: '>6 PD/月',\n          completionRate: 113,\n          status: 'success',\n          dataSource: '代码分析工具'\n        },\n        {\n          name: '维护提效PD',\n          current: '1.8 PD/月',\n          target: '>2 PD/月',\n          completionRate: 90,\n          status: 'warning',\n          dataSource: '版本管理系统'\n        }\n      ],\n      \n      // 成本维度一级指标\n      costL1Metrics: [\n        {\n          name: 'CDN节约',\n          currentSaving: 45000,\n          target: '>10%',\n          optimizationRate: 12.5,\n          progress: 125,\n          status: 'success',\n          dataSource: 'CDN服务商账单'\n        },\n        {\n          name: '计算资源节约',\n          currentSaving: 85000,\n          target: '>15%',\n          optimizationRate: 18.2,\n          progress: 121,\n          status: 'success',\n          dataSource: '云服务商账单'\n        },\n        {\n          name: '存储节约',\n          currentSaving: 28000,\n          target: '>8%',\n          optimizationRate: 9.8,\n          progress: 122,\n          status: 'success',\n          dataSource: '云服务商账单'\n        },\n        {\n          name: '带宽节约',\n          currentSaving: 27000,\n          target: '>12%',\n          optimizationRate: 14.5,\n          progress: 120,\n          status: 'success',\n          dataSource: '云服务商账单'\n        }\n      ],\n      \n      // 成本维度二级指标\n      costL2Metrics: [\n        {\n          name: '缓存节约',\n          currentSaving: 6200,\n          target: '>5,000元/月',\n          completionRate: 124,\n          status: 'success',\n          dataSource: 'CDN服务商',\n          trend: 'up',\n          trendText: '↗ +8.5%'\n        },\n        {\n          name: '压缩节约',\n          currentSaving: 3800,\n          target: '>3,000元/月',\n          completionRate: 126,\n          status: 'success',\n          dataSource: 'CDN服务商',\n          trend: 'up',\n          trendText: '↗ +12.3%'\n        },\n        {\n          name: '分发节约',\n          currentSaving: 2100,\n          target: '>2,000元/月',\n          completionRate: 105,\n          status: 'success',\n          dataSource: 'CDN服务商',\n          trend: 'stable',\n          trendText: '→ +2.1%'\n        },\n        {\n          name: 'CPU节约',\n          currentSaving: 9200,\n          target: '>8,000元/月',\n          completionRate: 115,\n          status: 'success',\n          dataSource: '云服务商',\n          trend: 'up',\n          trendText: '↗ +15.2%'\n        },\n        {\n          name: '内存节约',\n          currentSaving: 6800,\n          target: '>6,000元/月',\n          completionRate: 113,\n          status: 'success',\n          dataSource: '云服务商',\n          trend: 'up',\n          trendText: '↗ +9.8%'\n        },\n        {\n          name: '实例节约',\n          currentSaving: 3800,\n          target: '>4,000元/月',\n          completionRate: 95,\n          status: 'warning',\n          dataSource: '云服务商',\n          trend: 'down',\n          trendText: '↘ -2.5%'\n        }\n      ],\n      \n      // 重点执行指标\n      keyMetrics: [\n        {\n          name: '增量编译节约',\n          current: '68%',\n          target: '>60%',\n          dataSource: '构建系统',\n          status: 'success',\n          statusText: '达标'\n        },\n        {\n          name: '告警准确率',\n          current: '92%',\n          target: '>90%',\n          dataSource: '监控系统',\n          status: 'success',\n          statusText: '达标'\n        },\n        {\n          name: '组件使用频次',\n          current: '58次/组件',\n          target: '>50次/组件',\n          dataSource: '代码分析工具',\n          status: 'success',\n          statusText: '达标'\n        }\n      ],\n      \n      // 数据源状态\n      dataSources: [\n        {\n          name: '构建系统',\n          status: 'online',\n          lastUpdate: '2024-06-20 15:30:00'\n        },\n        {\n          name: '部署平台',\n          status: 'online',\n          lastUpdate: '2024-06-20 15:28:00'\n        },\n        {\n          name: '监控系统',\n          status: 'online',\n          lastUpdate: '2024-06-20 15:32:00'\n        },\n        {\n          name: '工单系统',\n          status: 'warning',\n          lastUpdate: '2024-06-20 14:45:00'\n        },\n        {\n          name: 'CDN服务商',\n          status: 'online',\n          lastUpdate: '2024-06-20 15:25:00'\n        },\n        {\n          name: '云服务商',\n          status: 'online',\n          lastUpdate: '2024-06-20 15:20:00'\n        }\n      ]\n    }\n  }\n}\n</script>\n\n<style scoped>\n.dashboard {\n  padding: 20px;\n  max-width: 1400px;\n  margin: 0 auto;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n}\n\n/* 页面标题样式 */\n.dashboard-header {\n  text-align: center;\n  margin-bottom: 40px;\n  padding: 40px 0;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border-radius: 12px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n}\n\n.dashboard-header h1 {\n  font-size: 2.5rem;\n  margin-bottom: 10px;\n  font-weight: 700;\n}\n\n.dashboard-header .subtitle {\n  font-size: 1.2rem;\n  opacity: 0.9;\n  margin: 0 0 20px 0;\n}\n\n/* 导航样式 */\n.main-nav {\n  display: flex;\n  justify-content: center;\n  gap: 20px;\n  margin-top: 20px;\n}\n\n.nav-link {\n  color: white;\n  text-decoration: none;\n  padding: 10px 20px;\n  border-radius: 25px;\n  background: rgba(255, 255, 255, 0.1);\n  transition: all 0.3s ease;\n  font-weight: 500;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.nav-link:hover {\n  background: rgba(255, 255, 255, 0.2);\n  transform: translateY(-2px);\n}\n\n.nav-link.active {\n  background: rgba(255, 255, 255, 0.3);\n  border-color: rgba(255, 255, 255, 0.5);\n}\n\n/* 北极星指标样式 */\n.overview-section {\n  margin-bottom: 40px;\n}\n\n.overview-section h2 {\n  color: #2c3e50;\n  margin-bottom: 20px;\n  border-bottom: 2px solid #3498db;\n  padding-bottom: 10px;\n}\n\n.metric-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n}\n\n.metric-card {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.metric-card.efficiency {\n  border-left: 4px solid #e74c3c;\n}\n\n.metric-card.cost {\n  border-left: 4px solid #27ae60;\n}\n\n.metric-icon {\n  font-size: 3em;\n}\n\n.metric-content h3 {\n  margin: 0 0 10px 0;\n  color: #2c3e50;\n  font-size: 1.1em;\n}\n\n.metric-value {\n  font-size: 2.5em;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 5px;\n}\n\n.metric-trend {\n  font-size: 1.1em;\n  font-weight: 500;\n}\n\n.metric-trend.positive {\n  color: #27ae60;\n}\n\n/* 提效维度样式 */\n.efficiency-section {\n  margin-bottom: 40px;\n}\n\n.efficiency-section h2 {\n  color: #2c3e50;\n  margin-bottom: 20px;\n  border-bottom: 2px solid #e74c3c;\n  padding-bottom: 10px;\n}\n\n.level-one-metrics {\n  margin-bottom: 30px;\n}\n\n.level-one-metrics h3 {\n  color: #34495e;\n  margin-bottom: 15px;\n}\n\n.metrics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 20px;\n}\n\n.metric-item {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.metric-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.metric-name {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.metric-target {\n  color: #7f8c8d;\n  font-size: 0.9em;\n}\n\n.metric-progress {\n  margin-bottom: 10px;\n}\n\n.progress-bar {\n  width: 100%;\n  height: 8px;\n  background-color: #ecf0f1;\n  border-radius: 4px;\n  overflow: hidden;\n  margin-bottom: 5px;\n}\n\n.progress-fill {\n  height: 100%;\n  background-color: #e74c3c;\n  transition: width 0.3s ease;\n}\n\n.progress-text {\n  font-size: 0.9em;\n  color: #7f8c8d;\n}\n\n.metric-details {\n  font-size: 0.8em;\n  color: #95a5a6;\n}\n\n/* 二级指标表格样式 */\n.level-two-metrics h3 {\n  color: #34495e;\n  margin-bottom: 20px;\n}\n\n.metric-category {\n  margin-bottom: 30px;\n}\n\n.metric-category h4 {\n  color: #2c3e50;\n  margin-bottom: 15px;\n  padding-left: 10px;\n  border-left: 3px solid #e74c3c;\n}\n\n.metrics-table {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.metrics-table table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.metrics-table th {\n  background-color: #f8f9fa;\n  padding: 15px;\n  text-align: left;\n  font-weight: 600;\n  color: #2c3e50;\n  border-bottom: 1px solid #dee2e6;\n}\n\n.metrics-table td {\n  padding: 15px;\n  border-bottom: 1px solid #dee2e6;\n}\n\n.completion-rate {\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 0.9em;\n  font-weight: 500;\n}\n\n.completion-rate.success {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n.completion-rate.warning {\n  background-color: #fff3cd;\n  color: #856404;\n}\n\n/* 成本维度样式 */\n.cost-section {\n  margin-bottom: 40px;\n}\n\n.cost-section h2 {\n  color: #2c3e50;\n  margin-bottom: 20px;\n  border-bottom: 2px solid #27ae60;\n  padding-bottom: 10px;\n}\n\n.cost-overview h3 {\n  color: #34495e;\n  margin-bottom: 15px;\n}\n\n.cost-metrics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.cost-metric-card {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  border-left: 4px solid #27ae60;\n}\n\n.cost-metric-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.cost-metric-header h4 {\n  margin: 0;\n  color: #2c3e50;\n}\n\n.cost-target {\n  color: #7f8c8d;\n  font-size: 0.9em;\n}\n\n.cost-value {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.current-value {\n  font-size: 1.5em;\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.optimization-rate {\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-weight: 500;\n}\n\n.optimization-rate.success {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n.cost-progress .progress-fill {\n  background-color: #27ae60;\n}\n\n.cost-source {\n  font-size: 0.8em;\n  color: #95a5a6;\n  margin-top: 10px;\n}\n\n/* 成本详细表格 */\n.cost-detail {\n  margin-top: 30px;\n}\n\n.cost-detail h3 {\n  color: #34495e;\n  margin-bottom: 15px;\n}\n\n.cost-detail-table {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.trend {\n  font-weight: 500;\n}\n\n.trend.up {\n  color: #27ae60;\n}\n\n.trend.down {\n  color: #e74c3c;\n}\n\n.trend.stable {\n  color: #f39c12;\n}\n\n/* 重点执行指标样式 */\n.key-metrics-section {\n  margin-bottom: 40px;\n}\n\n.key-metrics-section h2 {\n  color: #2c3e50;\n  margin-bottom: 20px;\n  border-bottom: 2px solid #9b59b6;\n  padding-bottom: 10px;\n}\n\n.key-metrics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n}\n\n.key-metric-card {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  border-left: 4px solid #9b59b6;\n}\n\n.key-metric-icon {\n  font-size: 2em;\n}\n\n.key-metric-content {\n  flex: 1;\n}\n\n.key-metric-content h4 {\n  margin: 0 0 8px 0;\n  color: #2c3e50;\n}\n\n.key-metric-value {\n  font-size: 1.8em;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 5px;\n}\n\n.key-metric-target {\n  color: #7f8c8d;\n  font-size: 0.9em;\n  margin-bottom: 5px;\n}\n\n.key-metric-source {\n  color: #95a5a6;\n  font-size: 0.8em;\n}\n\n.key-metric-status {\n  padding: 6px 12px;\n  border-radius: 4px;\n  font-weight: 500;\n  font-size: 0.9em;\n}\n\n.key-metric-status.success {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n/* 数据采集状态样式 */\n.data-collection-section h2 {\n  color: #2c3e50;\n  margin-bottom: 20px;\n  border-bottom: 2px solid #34495e;\n  padding-bottom: 10px;\n}\n\n.collection-status {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.status-item {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 10px 0;\n  border-bottom: 1px solid #ecf0f1;\n}\n\n.status-item:last-child {\n  border-bottom: none;\n}\n\n.status-indicator {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n}\n\n.status-indicator.online {\n  background-color: #27ae60;\n}\n\n.status-indicator.warning {\n  background-color: #f39c12;\n}\n\n.status-indicator.offline {\n  background-color: #e74c3c;\n}\n\n.source-name {\n  font-weight: 500;\n  color: #2c3e50;\n  min-width: 120px;\n}\n\n.last-update {\n  color: #7f8c8d;\n  font-size: 0.9em;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .dashboard {\n    padding: 15px;\n  }\n  \n  .metric-cards {\n    grid-template-columns: 1fr;\n  }\n  \n  .metrics-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .cost-metrics-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .key-metrics-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .metrics-table {\n    overflow-x: auto;\n  }\n  \n  .page-header h1 {\n    font-size: 2em;\n  }\n}\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAW;;EACZA,KAAK,EAAC;AAAkB;;EAGzBA,KAAK,EAAC;AAAU;;EAOlBA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAwB;;EAE5BA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAuB;;EAGjCA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAuB;;EAOrCA,KAAK,EAAC;AAAoB;;EAIxBA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAc;;EAEhBA,KAAK,EAAC;AAAe;;EAClBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAe;;EAExBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAc;;EAGnBA,KAAK,EAAC;AAAe;;EAExBA,KAAK,EAAC;AAAgB;;EACnBA,KAAK,EAAC;AAAa;;EAO5BA,KAAK,EAAC;AAAmB;;EAIvBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAe;;EA6BvBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAe;;EA6BvBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAe;;EA+B3BA,KAAK,EAAC;AAAc;;EAIlBA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAmB;;EAErBA,KAAK,EAAC;AAAoB;;EAEvBA,KAAK,EAAC;AAAa;;EAEtBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAY;;EACfA,KAAK,EAAC;AAAe;;EAKxBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;EAItBA,KAAK,EAAC;AAAa;;EAO3BA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAmB;;EAoC7BA,KAAK,EAAC;AAAqB;;EAEzBA,KAAK,EAAC;AAAkB;;EAGpBA,KAAK,EAAC;AAAoB;;EAExBA,KAAK,EAAC;AAAkB;;EACxBA,KAAK,EAAC;AAAmB;;EACzBA,KAAK,EAAC;AAAmB;;EAUjCA,KAAK,EAAC;AAAyB;;EAE7BA,KAAK,EAAC;AAAmB;;EAGpBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;;uBA7PjCC,mBAAA,CAiQM,OAjQNC,UAiQM,GAhQJC,mBAAA,CAOS,UAPTC,UAOS,G,0BANPD,mBAAA,CAAsB,YAAlB,eAAa,qB,0BACjBA,mBAAA,CAAuC;IAApCH,KAAK,EAAC;EAAU,GAAC,iBAAe,qBACnCG,mBAAA,CAGM,OAHNE,UAGM,GAFJC,YAAA,CAAgGC,sBAAA;IAAnFC,EAAE,EAAC,GAAG;IAACR,KAAK,EAAAS,eAAA,EAAC,UAAU;MAAAC,MAAA,EAAmBC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAAY,MAAIC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;gCAClFR,YAAA,CAA8HC,sBAAA;IAAjHC,EAAE,EAAC,kBAAkB;IAACR,KAAK,EAAAS,eAAA,EAAC,UAAU;MAAAC,MAAA,EAAmBC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAA2B,MAAIC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;oCAIpHC,mBAAA,aAAgB,EAChBZ,mBAAA,CAoBM,OApBNa,UAoBM,G,0BAnBJb,mBAAA,CAAc,YAAV,OAAK,qBACTA,mBAAA,CAiBM,OAjBNc,UAiBM,GAhBJd,mBAAA,CAOM,OAPNe,UAOM,G,0BANJf,mBAAA,CAAgC;IAA3BH,KAAK,EAAC;EAAa,GAAC,GAAC,qBAC1BG,mBAAA,CAIM,OAJNgB,UAIM,G,0BAHJhB,mBAAA,CAAgB,YAAZ,SAAO,qBACXA,mBAAA,CAAqD,OAArDiB,UAAqD,EAAAC,gBAAA,CAAxBC,KAAA,CAAAC,YAAY,IAAG,KAAG,iBAC/CpB,mBAAA,CAAmE,OAAnEqB,UAAmE,EAAhC,GAAC,GAAAH,gBAAA,CAAGC,KAAA,CAAAG,cAAc,IAAG,OAAK,gB,KAGjEtB,mBAAA,CAOM,OAPNuB,WAOM,G,0BANJvB,mBAAA,CAAiC;IAA5BH,KAAK,EAAC;EAAa,GAAC,IAAE,qBAC3BG,mBAAA,CAIM,OAJNwB,WAIM,G,0BAHJxB,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAAsE,OAAtEyB,WAAsE,EAA5C,GAAC,GAAAP,gBAAA,CAAGC,KAAA,CAAAO,cAAc,CAACC,cAAc,oBAC3D3B,mBAAA,CAAoF,OAApF4B,WAAoF,EAAjD,IAAE,GAAAV,gBAAA,CAAGC,KAAA,CAAAU,gBAAgB,CAACF,cAAc,MAAK,IAAE,gB,SAMtFf,mBAAA,YAAe,EACfZ,mBAAA,CA0HM,OA1HN8B,WA0HM,G,4BAzHJ9B,mBAAA,CAAe,YAAX,QAAM,qBAEVY,mBAAA,YAAe,EACfZ,mBAAA,CAmBM,OAnBN+B,WAmBM,G,0BAlBJ/B,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAgBM,OAhBNgC,WAgBM,I,kBAfJlC,mBAAA,CAcMmC,SAAA,QAAAC,WAAA,CAdoCf,KAAA,CAAAgB,mBAAmB,EAA7BC,MAAM;yBAAtCtC,mBAAA,CAcM;MAdDD,KAAK,EAAC,aAAa;MAAwCwC,GAAG,EAAED,MAAM,CAACE;QAC1EtC,mBAAA,CAGM,OAHNuC,WAGM,GAFJvC,mBAAA,CAAkD,QAAlDwC,WAAkD,EAAAtB,gBAAA,CAArBkB,MAAM,CAACE,IAAI,kBACxCtC,mBAAA,CAA0D,QAA1DyC,WAA0D,EAA9B,MAAI,GAAAvB,gBAAA,CAAGkB,MAAM,CAACM,MAAM,iB,GAElD1C,mBAAA,CAKM,OALN2C,WAKM,GAJJ3C,mBAAA,CAEM,OAFN4C,WAEM,GADJ5C,mBAAA,CAAyE;MAApEH,KAAK,EAAC,eAAe;MAAEgD,KAAK,EAAAC,eAAA;QAAAC,KAAA,EAAUX,MAAM,CAACY,QAAQ;MAAA;+BAE5DhD,mBAAA,CAA6E,QAA7EiD,WAA6E,EAAA/B,gBAAA,CAA9CkB,MAAM,CAACc,OAAO,IAAG,KAAG,GAAAhC,gBAAA,CAAGkB,MAAM,CAACM,MAAM,iB,GAErE1C,mBAAA,CAEM,OAFNmD,WAEM,GADJnD,mBAAA,CAA8D,QAA9DoD,WAA8D,EAApC,QAAM,GAAAlC,gBAAA,CAAGkB,MAAM,CAACiB,UAAU,iB;sCAM5DzC,mBAAA,YAAe,EACfZ,mBAAA,CA+FM,OA/FNsD,WA+FM,G,4BA9FJtD,mBAAA,CAAe,YAAX,QAAM,qBAEVY,mBAAA,WAAc,EACdZ,mBAAA,CA4BM,OA5BNuD,WA4BM,G,4BA3BJvD,mBAAA,CAAc,YAAV,OAAK,qBACTA,mBAAA,CAyBM,OAzBNwD,WAyBM,GAxBJxD,mBAAA,CAuBQ,gB,4BAtBNA,mBAAA,CAQQ,gBAPNA,mBAAA,CAMK,aALHA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAa,YAAT,MAAI,E,uBAGZA,mBAAA,CAYQ,iB,kBAXNF,mBAAA,CAUKmC,SAAA,QAAAC,WAAA,CAVgBf,KAAA,CAAAsC,YAAY,EAAtBrB,MAAM;yBAAjBtC,mBAAA,CAUK;MAV+BuC,GAAG,EAAED,MAAM,CAACE;QAC9CtC,mBAAA,CAA0B,YAAAkB,gBAAA,CAAnBkB,MAAM,CAACE,IAAI,kBAClBtC,mBAAA,CAA6B,YAAAkB,gBAAA,CAAtBkB,MAAM,CAACc,OAAO,kBACrBlD,mBAAA,CAA4B,YAAAkB,gBAAA,CAArBkB,MAAM,CAACM,MAAM,kBACpB1C,mBAAA,CAIK,aAHHA,mBAAA,CAEO;MAFAH,KAAK,EAAAS,eAAA,qBAAsB8B,MAAM,CAACsB,MAAM;wBAC1CtB,MAAM,CAACuB,cAAc,IAAG,IAC7B,uB,GAEF3D,mBAAA,CAAgC,YAAAkB,gBAAA,CAAzBkB,MAAM,CAACiB,UAAU,iB;0CAOlCzC,mBAAA,aAAgB,EAChBZ,mBAAA,CA4BM,OA5BN4D,WA4BM,G,4BA3BJ5D,mBAAA,CAAgB,YAAZ,SAAO,qBACXA,mBAAA,CAyBM,OAzBN6D,WAyBM,GAxBJ7D,mBAAA,CAuBQ,gB,4BAtBNA,mBAAA,CAQQ,gBAPNA,mBAAA,CAMK,aALHA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAa,YAAT,MAAI,E,uBAGZA,mBAAA,CAYQ,iB,kBAXNF,mBAAA,CAUKmC,SAAA,QAAAC,WAAA,CAVgBf,KAAA,CAAA2C,cAAc,EAAxB1B,MAAM;yBAAjBtC,mBAAA,CAUK;MAViCuC,GAAG,EAAED,MAAM,CAACE;QAChDtC,mBAAA,CAA0B,YAAAkB,gBAAA,CAAnBkB,MAAM,CAACE,IAAI,kBAClBtC,mBAAA,CAA6B,YAAAkB,gBAAA,CAAtBkB,MAAM,CAACc,OAAO,kBACrBlD,mBAAA,CAA4B,YAAAkB,gBAAA,CAArBkB,MAAM,CAACM,MAAM,kBACpB1C,mBAAA,CAIK,aAHHA,mBAAA,CAEO;MAFAH,KAAK,EAAAS,eAAA,qBAAsB8B,MAAM,CAACsB,MAAM;wBAC1CtB,MAAM,CAACuB,cAAc,IAAG,IAC7B,uB,GAEF3D,mBAAA,CAAgC,YAAAkB,gBAAA,CAAzBkB,MAAM,CAACiB,UAAU,iB;0CAOlCzC,mBAAA,WAAc,EACdZ,mBAAA,CA4BM,OA5BN+D,WA4BM,G,4BA3BJ/D,mBAAA,CAAc,YAAV,OAAK,qBACTA,mBAAA,CAyBM,OAzBNgE,WAyBM,GAxBJhE,mBAAA,CAuBQ,gB,4BAtBNA,mBAAA,CAQQ,gBAPNA,mBAAA,CAMK,aALHA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAa,YAAT,MAAI,E,uBAGZA,mBAAA,CAYQ,iB,kBAXNF,mBAAA,CAUKmC,SAAA,QAAAC,WAAA,CAVgBf,KAAA,CAAA8C,gBAAgB,EAA1B7B,MAAM;yBAAjBtC,mBAAA,CAUK;MAVmCuC,GAAG,EAAED,MAAM,CAACE;QAClDtC,mBAAA,CAA0B,YAAAkB,gBAAA,CAAnBkB,MAAM,CAACE,IAAI,kBAClBtC,mBAAA,CAA6B,YAAAkB,gBAAA,CAAtBkB,MAAM,CAACc,OAAO,kBACrBlD,mBAAA,CAA4B,YAAAkB,gBAAA,CAArBkB,MAAM,CAACM,MAAM,kBACpB1C,mBAAA,CAIK,aAHHA,mBAAA,CAEO;MAFAH,KAAK,EAAAS,eAAA,qBAAsB8B,MAAM,CAACsB,MAAM;wBAC1CtB,MAAM,CAACuB,cAAc,IAAG,IAC7B,uB,GAEF3D,mBAAA,CAAgC,YAAAkB,gBAAA,CAAzBkB,MAAM,CAACiB,UAAU,iB;8CAStCzC,mBAAA,YAAe,EACfZ,mBAAA,CAkEM,OAlENkE,WAkEM,G,4BAjEJlE,mBAAA,CAAe,YAAX,QAAM,qBAEVY,mBAAA,YAAe,EACfZ,mBAAA,CAwBM,OAxBNmE,WAwBM,G,4BAvBJnE,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAqBM,OArBNoE,WAqBM,I,kBApBJtE,mBAAA,CAmBMmC,SAAA,QAAAC,WAAA,CAnByCf,KAAA,CAAAkD,aAAa,EAAvBjC,MAAM;yBAA3CtC,mBAAA,CAmBM;MAnBDD,KAAK,EAAC,kBAAkB;MAAkCwC,GAAG,EAAED,MAAM,CAACE;QACzEtC,mBAAA,CAGM,OAHNsE,WAGM,GAFJtE,mBAAA,CAA0B,YAAAkB,gBAAA,CAAnBkB,MAAM,CAACE,IAAI,kBAClBtC,mBAAA,CAAwD,QAAxDuE,WAAwD,EAA9B,MAAI,GAAArD,gBAAA,CAAGkB,MAAM,CAACM,MAAM,iB,GAEhD1C,mBAAA,CAaM,OAbNwE,WAaM,GAZJxE,mBAAA,CAKM,OALNyE,WAKM,GAJJzE,mBAAA,CAA+E,QAA/E0E,WAA+E,EAAnD,GAAC,GAAAxD,gBAAA,CAAGkB,MAAM,CAACuC,aAAa,CAAChD,cAAc,oBACnE3B,mBAAA,CAEO;MAFDH,KAAK,EAAAS,eAAA,EAAC,mBAAmB,EAAS8B,MAAM,CAACsB,MAAM;wBAChDtB,MAAM,CAACwC,gBAAgB,IAAG,IAC/B,uB,GAEF5E,mBAAA,CAIM,OAJN6E,WAIM,GAHJ7E,mBAAA,CAEM,OAFN8E,WAEM,GADJ9E,mBAAA,CAAyE;MAApEH,KAAK,EAAC,eAAe;MAAEgD,KAAK,EAAAC,eAAA;QAAAC,KAAA,EAAUX,MAAM,CAACY,QAAQ;MAAA;iCAG9DhD,mBAAA,CAAsD,OAAtD+E,WAAsD,EAAA7D,gBAAA,CAA1BkB,MAAM,CAACiB,UAAU,iB;sCAMrDzC,mBAAA,YAAe,EACfZ,mBAAA,CAkCM,OAlCNgF,WAkCM,G,4BAjCJhF,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CA+BM,OA/BNiF,WA+BM,GA9BJjF,mBAAA,CA6BQ,gB,4BA5BNA,mBAAA,CASQ,gBARNA,mBAAA,CAOK,aANHA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAe,YAAX,QAAM,GACVA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAW,YAAP,IAAE,E,uBAGVA,mBAAA,CAiBQ,iB,kBAhBNF,mBAAA,CAeKmC,SAAA,QAAAC,WAAA,CAfgBf,KAAA,CAAA+D,aAAa,EAAvB9C,MAAM;yBAAjBtC,mBAAA,CAeK;MAfgCuC,GAAG,EAAED,MAAM,CAACE;QAC/CtC,mBAAA,CAA0B,YAAAkB,gBAAA,CAAnBkB,MAAM,CAACE,IAAI,kBAClBtC,mBAAA,CAAqD,YAAjD,GAAC,GAAAkB,gBAAA,CAAGkB,MAAM,CAACuC,aAAa,CAAChD,cAAc,oBAC3C3B,mBAAA,CAA4B,YAAAkB,gBAAA,CAArBkB,MAAM,CAACM,MAAM,kBACpB1C,mBAAA,CAIK,aAHHA,mBAAA,CAEO;MAFAH,KAAK,EAAAS,eAAA,qBAAsB8B,MAAM,CAACsB,MAAM;wBAC1CtB,MAAM,CAACuB,cAAc,IAAG,IAC7B,uB,GAEF3D,mBAAA,CAAgC,YAAAkB,gBAAA,CAAzBkB,MAAM,CAACiB,UAAU,kBACxBrD,mBAAA,CAIK,aAHHA,mBAAA,CAEO;MAFAH,KAAK,EAAAS,eAAA,WAAY8B,MAAM,CAAC+C,KAAK;wBAC/B/C,MAAM,CAACgD,SAAS,wB;4CAUnCxE,mBAAA,YAAe,EACfZ,mBAAA,CAgBM,OAhBNqF,WAgBM,G,4BAfJrF,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAaM,OAbNsF,WAaM,I,kBAZJxF,mBAAA,CAWMmC,SAAA,QAAAC,WAAA,CAXwCf,KAAA,CAAAoE,UAAU,EAApBnD,MAAM;yBAA1CtC,mBAAA,CAWM;MAXDD,KAAK,EAAC,iBAAiB;MAA+BwC,GAAG,EAAED,MAAM,CAACE;oCACrEtC,mBAAA,CAAqC;MAAhCH,KAAK,EAAC;IAAiB,GAAC,IAAE,qBAC/BG,mBAAA,CAKM,OALNwF,WAKM,GAJJxF,mBAAA,CAA0B,YAAAkB,gBAAA,CAAnBkB,MAAM,CAACE,IAAI,kBAClBtC,mBAAA,CAAwD,OAAxDyF,WAAwD,EAAAvE,gBAAA,CAAvBkB,MAAM,CAACc,OAAO,kBAC/ClD,mBAAA,CAA4D,OAA5D0F,WAA4D,EAA7B,MAAI,GAAAxE,gBAAA,CAAGkB,MAAM,CAACM,MAAM,kBACnD1C,mBAAA,CAA4D,OAA5D2F,WAA4D,EAAAzE,gBAAA,CAA1BkB,MAAM,CAACiB,UAAU,iB,GAErDrD,mBAAA,CAEM;MAFDH,KAAK,EAAAS,eAAA,EAAC,mBAAmB,EAAS8B,MAAM,CAACsB,MAAM;wBAC/CtB,MAAM,CAACwD,UAAU,wB;sCAM5BhF,mBAAA,YAAe,EACfZ,mBAAA,CASM,OATN6F,WASM,G,4BARJ7F,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAMM,OANN8F,WAMM,I,kBALJhG,mBAAA,CAIMmC,SAAA,QAAAC,WAAA,CAJoCf,KAAA,CAAA4E,WAAW,EAArBC,MAAM;yBAAtClG,mBAAA,CAIM;MAJDD,KAAK,EAAC,aAAa;MAAgCwC,GAAG,EAAE2D,MAAM,CAAC1D;QAClEtC,mBAAA,CAA2D;MAAtDH,KAAK,EAAAS,eAAA,EAAC,kBAAkB,EAAS0F,MAAM,CAACtC,MAAM;6BACnD1D,mBAAA,CAAkD,QAAlDiG,WAAkD,EAAA/E,gBAAA,CAArB8E,MAAM,CAAC1D,IAAI,kBACxCtC,mBAAA,CAA8D,QAA9DkG,WAA8D,EAApC,QAAM,GAAAhF,gBAAA,CAAG8E,MAAM,CAACG,UAAU,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}