{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nimport _imports_0 from './assets/logo.png';\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_HelloWorld = _resolveComponent(\"HelloWorld\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_cache[0] || (_cache[0] = _createElementVNode(\"img\", {\n    alt: \"Vue logo\",\n    src: _imports_0\n  }, null, -1 /* CACHED */)), _createVNode(_component_HelloWorld, {\n    msg: \"Welcome to Your Vue.js App\"\n  })], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["_imports_0", "_createElementVNode", "alt", "src", "_createVNode", "_component_HelloWorld", "msg"], "sources": ["/Users/<USER>/Desktop/dev/frontend-project/src/App.vue"], "sourcesContent": ["<template>\n  <img alt=\"Vue logo\" src=\"./assets/logo.png\">\n  <HelloWorld msg=\"Welcome to Your Vue.js App\"/>\n</template>\n\n<script>\nimport HelloWorld from './components/HelloWorld.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    HelloWorld\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-align: center;\n  color: #2c3e50;\n  margin-top: 60px;\n}\n</style>\n"], "mappings": ";OACsBA,UAAuB;;;uFAA3CC,mBAAA,CAA4C;IAAvCC,GAAG,EAAC,UAAU;IAACC,GAAuB,EAAvBH;8BACpBI,YAAA,CAA8CC,qBAAA;IAAlCC,GAAG,EAAC;EAA4B,G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}