{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, createStaticVNode as _createStaticVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"hello\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"h1\", null, _toDisplayString($props.msg), 1 /* TEXT */), _cache[0] || (_cache[0] = _createStaticVNode(\"<p data-v-469af010> For a guide and recipes on how to configure / customize this project,<br data-v-469af010> check out the <a href=\\\"https://cli.vuejs.org\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\" data-v-469af010>vue-cli documentation</a>. </p><h3 data-v-469af010>Installed CLI Plugins</h3><ul data-v-469af010><li data-v-469af010><a href=\\\"https://github.com/vuejs/vue-cli/tree/dev/packages/%40vue/cli-plugin-babel\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\" data-v-469af010>babel</a></li><li data-v-469af010><a href=\\\"https://github.com/vuejs/vue-cli/tree/dev/packages/%40vue/cli-plugin-eslint\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\" data-v-469af010>eslint</a></li></ul><h3 data-v-469af010>Essential Links</h3><ul data-v-469af010><li data-v-469af010><a href=\\\"https://vuejs.org\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\" data-v-469af010>Core Docs</a></li><li data-v-469af010><a href=\\\"https://forum.vuejs.org\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\" data-v-469af010>Forum</a></li><li data-v-469af010><a href=\\\"https://chat.vuejs.org\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\" data-v-469af010>Community Chat</a></li><li data-v-469af010><a href=\\\"https://twitter.com/vuejs\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\" data-v-469af010>Twitter</a></li><li data-v-469af010><a href=\\\"https://news.vuejs.org\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\" data-v-469af010>News</a></li></ul><h3 data-v-469af010>Ecosystem</h3><ul data-v-469af010><li data-v-469af010><a href=\\\"https://router.vuejs.org\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\" data-v-469af010>vue-router</a></li><li data-v-469af010><a href=\\\"https://vuex.vuejs.org\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\" data-v-469af010>vuex</a></li><li data-v-469af010><a href=\\\"https://github.com/vuejs/vue-devtools#vue-devtools\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\" data-v-469af010>vue-devtools</a></li><li data-v-469af010><a href=\\\"https://vue-loader.vuejs.org\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\" data-v-469af010>vue-loader</a></li><li data-v-469af010><a href=\\\"https://github.com/vuejs/awesome-vue\\\" target=\\\"_blank\\\" rel=\\\"noopener\\\" data-v-469af010>awesome-vue</a></li></ul>\", 7))]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_toDisplayString", "$props", "msg"], "sources": ["/Users/<USER>/Desktop/dev/frontend-project/src/components/HelloWorld.vue"], "sourcesContent": ["<template>\n  <div class=\"hello\">\n    <h1>{{ msg }}</h1>\n    <p>\n      For a guide and recipes on how to configure / customize this project,<br>\n      check out the\n      <a href=\"https://cli.vuejs.org\" target=\"_blank\" rel=\"noopener\">vue-cli documentation</a>.\n    </p>\n    <h3>Installed CLI Plugins</h3>\n    <ul>\n      <li><a href=\"https://github.com/vuejs/vue-cli/tree/dev/packages/%40vue/cli-plugin-babel\" target=\"_blank\" rel=\"noopener\">babel</a></li>\n      <li><a href=\"https://github.com/vuejs/vue-cli/tree/dev/packages/%40vue/cli-plugin-eslint\" target=\"_blank\" rel=\"noopener\">eslint</a></li>\n    </ul>\n    <h3>Essential Links</h3>\n    <ul>\n      <li><a href=\"https://vuejs.org\" target=\"_blank\" rel=\"noopener\">Core Docs</a></li>\n      <li><a href=\"https://forum.vuejs.org\" target=\"_blank\" rel=\"noopener\">Forum</a></li>\n      <li><a href=\"https://chat.vuejs.org\" target=\"_blank\" rel=\"noopener\">Community Chat</a></li>\n      <li><a href=\"https://twitter.com/vuejs\" target=\"_blank\" rel=\"noopener\">Twitter</a></li>\n      <li><a href=\"https://news.vuejs.org\" target=\"_blank\" rel=\"noopener\">News</a></li>\n    </ul>\n    <h3>Ecosystem</h3>\n    <ul>\n      <li><a href=\"https://router.vuejs.org\" target=\"_blank\" rel=\"noopener\">vue-router</a></li>\n      <li><a href=\"https://vuex.vuejs.org\" target=\"_blank\" rel=\"noopener\">vuex</a></li>\n      <li><a href=\"https://github.com/vuejs/vue-devtools#vue-devtools\" target=\"_blank\" rel=\"noopener\">vue-devtools</a></li>\n      <li><a href=\"https://vue-loader.vuejs.org\" target=\"_blank\" rel=\"noopener\">vue-loader</a></li>\n      <li><a href=\"https://github.com/vuejs/awesome-vue\" target=\"_blank\" rel=\"noopener\">awesome-vue</a></li>\n    </ul>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'HelloWorld',\n  props: {\n    msg: String\n  }\n}\n</script>\n\n<!-- Add \"scoped\" attribute to limit CSS to this component only -->\n<style scoped>\nh3 {\n  margin: 40px 0 0;\n}\nul {\n  list-style-type: none;\n  padding: 0;\n}\nli {\n  display: inline-block;\n  margin: 0 10px;\n}\na {\n  color: #42b983;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAO;;uBAAlBC,mBAAA,CA4BM,OA5BNC,UA4BM,GA3BJC,mBAAA,CAAkB,YAAAC,gBAAA,CAAXC,MAAA,CAAAC,GAAG,kB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}