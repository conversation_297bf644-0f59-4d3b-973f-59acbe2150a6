{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, createTextVNode as _createTextVNode, normalizeStyle as _normalizeStyle } from \"vue\";\nconst _hoisted_1 = {\n  class: \"efficiency-metrics\"\n};\nconst _hoisted_2 = {\n  class: \"metrics-selector\"\n};\nconst _hoisted_3 = {\n  class: \"selector-tabs\"\n};\nconst _hoisted_4 = [\"onClick\"];\nconst _hoisted_5 = {\n  key: 0,\n  class: \"metrics-section\"\n};\nconst _hoisted_6 = {\n  class: \"metrics-grid\"\n};\nconst _hoisted_7 = {\n  class: \"metric-header\"\n};\nconst _hoisted_8 = {\n  class: \"metric-unit\"\n};\nconst _hoisted_9 = {\n  class: \"metric-info\"\n};\nconst _hoisted_10 = {\n  class: \"metric-desc\"\n};\nconst _hoisted_11 = {\n  class: \"metric-source\"\n};\nconst _hoisted_12 = {\n  class: \"metric-target\"\n};\nconst _hoisted_13 = {\n  class: \"target-bar\"\n};\nconst _hoisted_14 = {\n  class: \"target-text\"\n};\nconst _hoisted_15 = {\n  class: \"level2-section\"\n};\nconst _hoisted_16 = {\n  class: \"level2-grid\"\n};\nconst _hoisted_17 = {\n  class: \"metric-header\"\n};\nconst _hoisted_18 = {\n  class: \"metric-unit\"\n};\nconst _hoisted_19 = {\n  class: \"metric-formula\"\n};\nconst _hoisted_20 = {\n  class: \"metric-target\"\n};\nconst _hoisted_21 = {\n  class: \"target-bar\"\n};\nconst _hoisted_22 = {\n  class: \"target-text\"\n};\nconst _hoisted_23 = {\n  key: 1,\n  class: \"metrics-section\"\n};\nconst _hoisted_24 = {\n  class: \"metrics-grid\"\n};\nconst _hoisted_25 = {\n  class: \"metric-header\"\n};\nconst _hoisted_26 = {\n  class: \"metric-unit\"\n};\nconst _hoisted_27 = {\n  class: \"metric-info\"\n};\nconst _hoisted_28 = {\n  class: \"metric-desc\"\n};\nconst _hoisted_29 = {\n  class: \"metric-source\"\n};\nconst _hoisted_30 = {\n  class: \"metric-target\"\n};\nconst _hoisted_31 = {\n  class: \"target-bar\"\n};\nconst _hoisted_32 = {\n  class: \"target-text\"\n};\nconst _hoisted_33 = {\n  class: \"level2-section\"\n};\nconst _hoisted_34 = {\n  class: \"level2-grid\"\n};\nconst _hoisted_35 = {\n  class: \"metric-header\"\n};\nconst _hoisted_36 = {\n  class: \"metric-unit\"\n};\nconst _hoisted_37 = {\n  class: \"metric-formula\"\n};\nconst _hoisted_38 = {\n  class: \"metric-target\"\n};\nconst _hoisted_39 = {\n  class: \"target-bar\"\n};\nconst _hoisted_40 = {\n  class: \"target-text\"\n};\nconst _hoisted_41 = {\n  class: \"trend-charts\"\n};\nconst _hoisted_42 = {\n  class: \"chart-tabs\"\n};\nconst _hoisted_43 = [\"onClick\"];\nconst _hoisted_44 = {\n  class: \"chart-container\"\n};\nconst _hoisted_45 = {\n  class: \"chart-placeholder\"\n};\nconst _hoisted_46 = {\n  class: \"chart-mock\"\n};\nconst _hoisted_47 = {\n  class: \"chart-title\"\n};\nconst _hoisted_48 = {\n  class: \"chart-mock-content\"\n};\nconst _hoisted_49 = {\n  class: \"chart-mock-labels\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[5] || (_cache[5] = _createElementVNode(\"h2\", null, \"效率与成本指标\", -1 /* CACHED */)), _createCommentVNode(\" 指标选择器 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.metricTabs, tab => {\n    return _openBlock(), _createElementBlock(\"button\", {\n      key: tab.id,\n      class: _normalizeClass(['tab-btn', {\n        active: $data.activeTab === tab.id\n      }]),\n      onClick: $event => $data.activeTab = tab.id\n    }, _toDisplayString(tab.name), 11 /* TEXT, CLASS, PROPS */, _hoisted_4);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 效率指标 \"), $data.activeTab === 'efficiency' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createCommentVNode(\" 一级指标 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.efficiencyMetrics.level1, metric => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: metric.id,\n      class: \"metric-card level1\"\n    }, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"h3\", null, _toDisplayString(metric.name), 1 /* TEXT */), _createElementVNode(\"div\", {\n      class: _normalizeClass([\"metric-value\", $options.getValueClass(metric)])\n    }, [_createTextVNode(_toDisplayString($options.formatValue(metric.value)) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_8, _toDisplayString(metric.unit), 1 /* TEXT */), _createElementVNode(\"span\", {\n      class: _normalizeClass([\"metric-trend\", $options.getTrendClass(metric.trend)])\n    }, _toDisplayString($options.formatTrend(metric.trend)), 3 /* TEXT, CLASS */)], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, _toDisplayString(metric.description), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_11, \"数据来源: \" + _toDisplayString(metric.source), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", {\n      class: \"target-progress\",\n      style: _normalizeStyle({\n        width: $options.getProgressWidth(metric)\n      })\n    }, null, 4 /* STYLE */)]), _createElementVNode(\"div\", _hoisted_14, \" 目标值: \" + _toDisplayString($options.formatValue(metric.target)) + \" \" + _toDisplayString(metric.unit), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */)), _createCommentVNode(\" 二级指标 \"), _createElementVNode(\"div\", _hoisted_15, [_cache[1] || (_cache[1] = _createElementVNode(\"h3\", null, \"二级指标明细\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_16, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.efficiencyMetrics.level2, metric => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: metric.id,\n      class: \"metric-card level2\"\n    }, [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"h4\", null, _toDisplayString(metric.name), 1 /* TEXT */), _createElementVNode(\"div\", {\n      class: _normalizeClass([\"metric-value\", $options.getValueClass(metric)])\n    }, [_createTextVNode(_toDisplayString($options.formatValue(metric.value)) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_18, _toDisplayString(metric.unit), 1 /* TEXT */), _createElementVNode(\"span\", {\n      class: _normalizeClass([\"metric-trend\", $options.getTrendClass(metric.trend)])\n    }, _toDisplayString($options.formatTrend(metric.trend)), 3 /* TEXT, CLASS */)], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_19, [_cache[0] || (_cache[0] = _createElementVNode(\"span\", {\n      class: \"formula-label\"\n    }, \"计算公式:\", -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString(metric.formula), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", {\n      class: \"target-progress\",\n      style: _normalizeStyle({\n        width: $options.getProgressWidth(metric)\n      })\n    }, null, 4 /* STYLE */)]), _createElementVNode(\"div\", _hoisted_22, \" 目标值: \" + _toDisplayString($options.formatValue(metric.target)) + \" \" + _toDisplayString(metric.unit), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */))])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 成本指标 \"), $data.activeTab === 'cost' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [_createCommentVNode(\" 一级指标 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.costMetrics.level1, metric => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: metric.id,\n      class: \"metric-card level1\"\n    }, [_createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"h3\", null, _toDisplayString(metric.name), 1 /* TEXT */), _createElementVNode(\"div\", {\n      class: _normalizeClass([\"metric-value\", $options.getValueClass(metric, true)])\n    }, [_createTextVNode(_toDisplayString($options.formatValue(metric.value)) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_26, _toDisplayString(metric.unit), 1 /* TEXT */), _createElementVNode(\"span\", {\n      class: _normalizeClass([\"metric-trend\", $options.getTrendClass(metric.trend, true)])\n    }, _toDisplayString($options.formatTrend(metric.trend)), 3 /* TEXT, CLASS */)], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, _toDisplayString(metric.description), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_29, \"数据来源: \" + _toDisplayString(metric.source), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_30, [_createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", {\n      class: \"target-progress\",\n      style: _normalizeStyle({\n        width: $options.getProgressWidth(metric, true)\n      })\n    }, null, 4 /* STYLE */)]), _createElementVNode(\"div\", _hoisted_32, \" 目标值: \" + _toDisplayString($options.formatValue(metric.target)) + \" \" + _toDisplayString(metric.unit), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */)), _createCommentVNode(\" 二级指标 \"), _createElementVNode(\"div\", _hoisted_33, [_cache[3] || (_cache[3] = _createElementVNode(\"h3\", null, \"二级指标明细\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_34, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.costMetrics.level2, metric => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: metric.id,\n      class: \"metric-card level2\"\n    }, [_createElementVNode(\"div\", _hoisted_35, [_createElementVNode(\"h4\", null, _toDisplayString(metric.name), 1 /* TEXT */), _createElementVNode(\"div\", {\n      class: _normalizeClass([\"metric-value\", $options.getValueClass(metric, true)])\n    }, [_createTextVNode(_toDisplayString($options.formatValue(metric.value)) + \" \", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_36, _toDisplayString(metric.unit), 1 /* TEXT */), _createElementVNode(\"span\", {\n      class: _normalizeClass([\"metric-trend\", $options.getTrendClass(metric.trend, true)])\n    }, _toDisplayString($options.formatTrend(metric.trend)), 3 /* TEXT, CLASS */)], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_37, [_cache[2] || (_cache[2] = _createElementVNode(\"span\", {\n      class: \"formula-label\"\n    }, \"计算公式:\", -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString(metric.formula), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"div\", _hoisted_39, [_createElementVNode(\"div\", {\n      class: \"target-progress\",\n      style: _normalizeStyle({\n        width: $options.getProgressWidth(metric, true)\n      })\n    }, null, 4 /* STYLE */)]), _createElementVNode(\"div\", _hoisted_40, \" 目标值: \" + _toDisplayString($options.formatValue(metric.target)) + \" \" + _toDisplayString(metric.unit), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */))])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 趋势图表 \"), _createElementVNode(\"div\", _hoisted_41, [_cache[4] || (_cache[4] = _createElementVNode(\"h3\", null, \"指标趋势分析\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_42, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.chartOptions, chart => {\n    return _openBlock(), _createElementBlock(\"button\", {\n      key: chart.id,\n      class: _normalizeClass(['chart-tab', {\n        active: $data.activeChart === chart.id\n      }]),\n      onClick: $event => $data.activeChart = chart.id\n    }, _toDisplayString(chart.name), 11 /* TEXT, CLASS, PROPS */, _hoisted_43);\n  }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"div\", _hoisted_44, [_createCommentVNode(\" 这里可以集成图表库，如ECharts或Chart.js \"), _createElementVNode(\"div\", _hoisted_45, [_createElementVNode(\"div\", _hoisted_46, [_createElementVNode(\"div\", _hoisted_47, _toDisplayString($options.getActiveChartTitle()), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_48, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.getMockChartData(), (bar, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      class: \"mock-bar\",\n      style: _normalizeStyle({\n        height: bar.height,\n        backgroundColor: bar.color\n      })\n    }, null, 4 /* STYLE */);\n  }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"div\", _hoisted_49, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.months, (month, index) => {\n    return _openBlock(), _createElementBlock(\"span\", {\n      key: index\n    }, _toDisplayString(month), 1 /* TEXT */);\n  }), 128 /* KEYED_FRAGMENT */))])])])])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createCommentVNode", "_hoisted_2", "_hoisted_3", "_Fragment", "_renderList", "$data", "metricTabs", "tab", "key", "id", "_normalizeClass", "active", "activeTab", "onClick", "$event", "name", "_hoisted_4", "_hoisted_5", "_hoisted_6", "efficiencyMetrics", "level1", "metric", "_hoisted_7", "_toDisplayString", "$options", "getValueClass", "formatValue", "value", "_hoisted_8", "unit", "getTrendClass", "trend", "formatTrend", "_hoisted_9", "_hoisted_10", "description", "_hoisted_11", "source", "_hoisted_12", "_hoisted_13", "style", "_normalizeStyle", "width", "getProgressWidth", "_hoisted_14", "target", "_hoisted_15", "_hoisted_16", "level2", "_hoisted_17", "_hoisted_18", "_hoisted_19", "formula", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "costMetrics", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "chartOptions", "chart", "activeChart", "_hoisted_43", "_hoisted_44", "_hoisted_45", "_hoisted_46", "_hoisted_47", "getActiveChartTitle", "_hoisted_48", "getMockChartData", "bar", "index", "height", "backgroundColor", "color", "_hoisted_49", "months", "month"], "sources": ["/Users/<USER>/Desktop/dev/frontend-project/src/components/EfficiencyMetrics.vue"], "sourcesContent": ["<template>\n  <div class=\"efficiency-metrics\">\n    <h2>效率与成本指标</h2>\n    \n    <!-- 指标选择器 -->\n    <div class=\"metrics-selector\">\n      <div class=\"selector-tabs\">\n        <button \n          v-for=\"tab in metricTabs\" \n          :key=\"tab.id\"\n          :class=\"['tab-btn', { active: activeTab === tab.id }]\"\n          @click=\"activeTab = tab.id\"\n        >\n          {{ tab.name }}\n        </button>\n      </div>\n    </div>\n    \n    <!-- 效率指标 -->\n    <div v-if=\"activeTab === 'efficiency'\" class=\"metrics-section\">\n      <div class=\"metrics-grid\">\n        <!-- 一级指标 -->\n        <div \n          v-for=\"metric in efficiencyMetrics.level1\" \n          :key=\"metric.id\"\n          class=\"metric-card level1\"\n        >\n          <div class=\"metric-header\">\n            <h3>{{ metric.name }}</h3>\n            <div class=\"metric-value\" :class=\"getValueClass(metric)\">\n              {{ formatValue(metric.value) }}\n              <span class=\"metric-unit\">{{ metric.unit }}</span>\n              <span \n                class=\"metric-trend\" \n                :class=\"getTrendClass(metric.trend)\"\n              >\n                {{ formatTrend(metric.trend) }}\n              </span>\n            </div>\n          </div>\n          <div class=\"metric-info\">\n            <div class=\"metric-desc\">{{ metric.description }}</div>\n            <div class=\"metric-source\">数据来源: {{ metric.source }}</div>\n          </div>\n          <div class=\"metric-target\">\n            <div class=\"target-bar\">\n              <div \n                class=\"target-progress\" \n                :style=\"{ width: getProgressWidth(metric) }\"\n              ></div>\n            </div>\n            <div class=\"target-text\">\n              目标值: {{ formatValue(metric.target) }} {{ metric.unit }}\n            </div>\n          </div>\n        </div>\n        \n        <!-- 二级指标 -->\n        <div class=\"level2-section\">\n          <h3>二级指标明细</h3>\n          <div class=\"level2-grid\">\n            <div \n              v-for=\"metric in efficiencyMetrics.level2\" \n              :key=\"metric.id\"\n              class=\"metric-card level2\"\n            >\n              <div class=\"metric-header\">\n                <h4>{{ metric.name }}</h4>\n                <div class=\"metric-value\" :class=\"getValueClass(metric)\">\n                  {{ formatValue(metric.value) }}\n                  <span class=\"metric-unit\">{{ metric.unit }}</span>\n                  <span \n                    class=\"metric-trend\" \n                    :class=\"getTrendClass(metric.trend)\"\n                  >\n                    {{ formatTrend(metric.trend) }}\n                  </span>\n                </div>\n              </div>\n              <div class=\"metric-formula\">\n                <span class=\"formula-label\">计算公式:</span> {{ metric.formula }}\n              </div>\n              <div class=\"metric-target\">\n                <div class=\"target-bar\">\n                  <div \n                    class=\"target-progress\" \n                    :style=\"{ width: getProgressWidth(metric) }\"\n                  ></div>\n                </div>\n                <div class=\"target-text\">\n                  目标值: {{ formatValue(metric.target) }} {{ metric.unit }}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 成本指标 -->\n    <div v-if=\"activeTab === 'cost'\" class=\"metrics-section\">\n      <div class=\"metrics-grid\">\n        <!-- 一级指标 -->\n        <div \n          v-for=\"metric in costMetrics.level1\" \n          :key=\"metric.id\"\n          class=\"metric-card level1\"\n        >\n          <div class=\"metric-header\">\n            <h3>{{ metric.name }}</h3>\n            <div class=\"metric-value\" :class=\"getValueClass(metric, true)\">\n              {{ formatValue(metric.value) }}\n              <span class=\"metric-unit\">{{ metric.unit }}</span>\n              <span \n                class=\"metric-trend\" \n                :class=\"getTrendClass(metric.trend, true)\"\n              >\n                {{ formatTrend(metric.trend) }}\n              </span>\n            </div>\n          </div>\n          <div class=\"metric-info\">\n            <div class=\"metric-desc\">{{ metric.description }}</div>\n            <div class=\"metric-source\">数据来源: {{ metric.source }}</div>\n          </div>\n          <div class=\"metric-target\">\n            <div class=\"target-bar\">\n              <div \n                class=\"target-progress\" \n                :style=\"{ width: getProgressWidth(metric, true) }\"\n              ></div>\n            </div>\n            <div class=\"target-text\">\n              目标值: {{ formatValue(metric.target) }} {{ metric.unit }}\n            </div>\n          </div>\n        </div>\n        \n        <!-- 二级指标 -->\n        <div class=\"level2-section\">\n          <h3>二级指标明细</h3>\n          <div class=\"level2-grid\">\n            <div \n              v-for=\"metric in costMetrics.level2\" \n              :key=\"metric.id\"\n              class=\"metric-card level2\"\n            >\n              <div class=\"metric-header\">\n                <h4>{{ metric.name }}</h4>\n                <div class=\"metric-value\" :class=\"getValueClass(metric, true)\">\n                  {{ formatValue(metric.value) }}\n                  <span class=\"metric-unit\">{{ metric.unit }}</span>\n                  <span \n                    class=\"metric-trend\" \n                    :class=\"getTrendClass(metric.trend, true)\"\n                  >\n                    {{ formatTrend(metric.trend) }}\n                  </span>\n                </div>\n              </div>\n              <div class=\"metric-formula\">\n                <span class=\"formula-label\">计算公式:</span> {{ metric.formula }}\n              </div>\n              <div class=\"metric-target\">\n                <div class=\"target-bar\">\n                  <div \n                    class=\"target-progress\" \n                    :style=\"{ width: getProgressWidth(metric, true) }\"\n                  ></div>\n                </div>\n                <div class=\"target-text\">\n                  目标值: {{ formatValue(metric.target) }} {{ metric.unit }}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 趋势图表 -->\n    <div class=\"trend-charts\">\n      <h3>指标趋势分析</h3>\n      <div class=\"chart-tabs\">\n        <button \n          v-for=\"chart in chartOptions\" \n          :key=\"chart.id\"\n          :class=\"['chart-tab', { active: activeChart === chart.id }]\"\n          @click=\"activeChart = chart.id\"\n        >\n          {{ chart.name }}\n        </button>\n      </div>\n      \n      <div class=\"chart-container\">\n        <!-- 这里可以集成图表库，如ECharts或Chart.js -->\n        <div class=\"chart-placeholder\">\n          <div class=\"chart-mock\">\n            <div class=\"chart-title\">{{ getActiveChartTitle() }}</div>\n            <div class=\"chart-mock-content\">\n              <div \n                v-for=\"(bar, index) in getMockChartData()\" \n                :key=\"index\"\n                class=\"mock-bar\"\n                :style=\"{ height: bar.height, backgroundColor: bar.color }\"\n              ></div>\n            </div>\n            <div class=\"chart-mock-labels\">\n              <span v-for=\"(month, index) in months\" :key=\"index\">{{ month }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'EfficiencyMetrics',\n  data() {\n    return {\n      activeTab: 'efficiency',\n      activeChart: 'pd_trend',\n      metricTabs: [\n        { id: 'efficiency', name: '效率指标' },\n        { id: 'cost', name: '成本指标' }\n      ],\n      chartOptions: [\n        { id: 'pd_trend', name: 'PD投入趋势' },\n        { id: 'cost_trend', name: '成本节约趋势' },\n        { id: 'build_time', name: '构建时间趋势' },\n        { id: 'issue_rate', name: '线上问题率趋势' }\n      ],\n      months: ['1月', '2月', '3月', '4月', '5月', '6月'],\n      \n      // 效率指标数据\n      efficiencyMetrics: {\n        level1: [\n          {\n            id: 'pd_efficiency',\n            name: 'PD效率',\n            value: 0.85,\n            target: 0.9,\n            unit: '',\n            trend: 0.05,\n            description: '衡量前端基建在提升开发效率方面的综合表现',\n            source: '多维度统计数据',\n            status: 'good'\n          },\n          {\n            id: 'build_efficiency',\n            name: '构建效率',\n            value: 75,\n            target: 90,\n            unit: '%',\n            trend: 0.15,\n            description: '构建过程的效率提升百分比',\n            source: '构建系统数据',\n            status: 'good'\n          },\n          {\n            id: 'problem_resolution',\n            name: '问题解决效率',\n            value: 65,\n            target: 80,\n            unit: '%',\n            trend: -0.05,\n            description: '问题解决速度的提升百分比',\n            source: '问题跟踪系统',\n            status: 'warning'\n          }\n        ],\n        level2: [\n          {\n            id: 'build_time',\n            name: '构建时间',\n            value: 3.5,\n            target: 2.5,\n            unit: '分钟',\n            trend: -0.8,\n            formula: '平均构建时间',\n            status: 'good'\n          },\n          {\n            id: 'dev_setup',\n            name: '开发环境搭建时间',\n            value: 15,\n            target: 10,\n            unit: '分钟',\n            trend: -5,\n            formula: '平均环境搭建时间',\n            status: 'good'\n          },\n          {\n            id: 'code_reuse',\n            name: '代码复用率',\n            value: 65,\n            target: 80,\n            unit: '%',\n            trend: 10,\n            formula: '复用代码行数 / 总代码行数',\n            status: 'good'\n          },\n          {\n            id: 'test_coverage',\n            name: '测试覆盖率',\n            value: 70,\n            target: 85,\n            unit: '%',\n            trend: 5,\n            formula: '测试覆盖的代码行数 / 总代码行数',\n            status: 'warning'\n          },\n          {\n            id: 'issue_resolution',\n            name: '问题解决时间',\n            value: 4.2,\n            target: 3,\n            unit: '小时',\n            trend: 0.2,\n            formula: '平均问题解决时间',\n            status: 'danger'\n          },\n          {\n            id: 'deployment_frequency',\n            name: '部署频率',\n            value: 3.5,\n            target: 5,\n            unit: '次/周',\n            trend: 0.5,\n            formula: '每周平均部署次数',\n            status: 'warning'\n          }\n        ]\n      },\n      \n      // 成本指标数据\n      costMetrics: {\n        level1: [\n          {\n            id: 'cost_saving',\n            name: '成本节约',\n            value: 25,\n            target: 30,\n            unit: '%',\n            trend: 5,\n            description: '前端基建带来的总体成本节约比例',\n            source: '财务数据与工时统计',\n            status: 'good'\n          },\n          {\n            id: 'resource_utilization',\n            name: '资源利用率',\n            value: 70,\n            target: 85,\n            unit: '%',\n            trend: 8,\n            description: '资源利用效率提升百分比',\n            source: '资源监控系统',\n            status: 'good'\n          },\n          {\n            id: 'maintenance_cost',\n            name: '维护成本',\n            value: 35,\n            target: 25,\n            unit: '%',\n            trend: -5,\n            description: '维护成本占总成本的百分比',\n            source: '工时与财务数据',\n            status: 'warning'\n          }\n        ],\n        level2: [\n          {\n            id: 'labor_cost',\n            name: '人力成本',\n            value: 28,\n            target: 20,\n            unit: '%',\n            trend: -3,\n            formula: '人力成本 / 总成本',\n            status: 'warning'\n          },\n          {\n            id: 'infrastructure_cost',\n            name: '基础设施成本',\n            value: 15,\n            target: 12,\n            unit: '%',\n            trend: -2,\n            formula: '基础设施成本 / 总成本',\n            status: 'warning'\n          },\n          {\n            id: 'error_cost',\n            name: '错误修复成本',\n            value: 8,\n            target: 5,\n            unit: '%',\n            trend: -1.5,\n            formula: '错误修复成本 / 总成本',\n            status: 'warning'\n          },\n          {\n            id: 'time_to_market',\n            name: '上市时间',\n            value: 25,\n            target: 15,\n            unit: '天',\n            trend: -5,\n            formula: '平均产品上市时间',\n            status: 'good'\n          },\n          {\n            id: 'resource_saving',\n            name: '资源节约',\n            value: 30,\n            target: 40,\n            unit: '%',\n            trend: 8,\n            formula: '(旧资源使用 - 新资源使用) / 旧资源使用',\n            status: 'good'\n          },\n          {\n            id: 'tech_debt',\n            name: '技术债务',\n            value: 20,\n            target: 10,\n            unit: '%',\n            trend: -5,\n            formula: '技术债务工时 / 总工时',\n            status: 'warning'\n          }\n        ]\n      }\n    }\n  },\n  \n  methods: {\n    formatValue(value) {\n      if (typeof value === 'number') {\n        return value % 1 === 0 ? value : value.toFixed(1)\n      }\n      return value\n    },\n    \n    formatTrend(trend) {\n      if (trend === 0) return '持平'\n      const prefix = trend > 0 ? '+' : ''\n      return `${prefix}${trend}%`\n    },\n    \n    getValueClass(metric, isCost = false) {\n      if (isCost) {\n        // 对于成本指标，值越低越好\n        if (metric.value <= metric.target) return 'value-good'\n        if (metric.value <= metric.target * 1.2) return 'value-warning'\n        return 'value-danger'\n      } else {\n        // 对于效率指标，值越高越好\n        if (metric.value >= metric.target) return 'value-good'\n        if (metric.value >= metric.target * 0.8) return 'value-warning'\n        return 'value-danger'\n      }\n    },\n    \n    getTrendClass(trend, isCost = false) {\n      if (trend === 0) return 'trend-neutral'\n      \n      if (isCost) {\n        // 对于成本指标，趋势下降是好的\n        return trend < 0 ? 'trend-good' : 'trend-bad'\n      } else {\n        // 对于效率指标，趋势上升是好的\n        return trend > 0 ? 'trend-good' : 'trend-bad'\n      }\n    },\n    \n    getProgressWidth(metric, isCost = false) {\n      if (isCost) {\n        // 对于成本指标，值越低越好\n        const ratio = metric.target / Math.max(metric.value, metric.target)\n        return `${Math.min(ratio * 100, 100)}%`\n      } else {\n        // 对于效率指标，值越高越好\n        const ratio = metric.value / Math.max(metric.value, metric.target)\n        return `${Math.min(ratio * 100, 100)}%`\n      }\n    },\n    \n    getActiveChartTitle() {\n      const chart = this.chartOptions.find(c => c.id === this.activeChart)\n      return chart ? chart.name : ''\n    },\n    \n    getMockChartData() {\n      // 生成模拟图表数据\n      const data = []\n      let color = '#3498db'\n      \n      switch (this.activeChart) {\n        case 'pd_trend':\n          color = '#3498db'\n          data.push(\n            { height: '40%', color },\n            { height: '45%', color },\n            { height: '55%', color },\n            { height: '60%', color },\n            { height: '70%', color },\n            { height: '85%', color }\n          )\n          break\n        case 'cost_trend':\n          color = '#e74c3c'\n          data.push(\n            { height: '80%', color },\n            { height: '75%', color },\n            { height: '65%', color },\n            { height: '55%', color },\n            { height: '40%', color },\n            { height: '25%', color }\n          )\n          break\n        case 'build_time':\n          color = '#2ecc71'\n          data.push(\n            { height: '90%', color },\n            { height: '80%', color },\n            { height: '70%', color },\n            { height: '60%', color },\n            { height: '50%', color },\n            { height: '35%', color }\n          )\n          break\n        case 'issue_rate':\n          color = '#f39c12'\n          data.push(\n            { height: '75%', color },\n            { height: '65%', color },\n            { height: '70%', color },\n            { height: '60%', color },\n            { height: '50%', color },\n            { height: '45%', color }\n          )\n          break\n        default:\n          data.push(\n            { height: '50%', color },\n            { height: '60%', color },\n            { height: '70%', color },\n            { height: '65%', color },\n            { height: '75%', color },\n            { height: '80%', color }\n          )\n      }\n      \n      return data\n    }\n  }\n}\n</script>\n\n<style scoped>\n.efficiency-metrics {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.efficiency-metrics h2 {\n  color: #2c3e50;\n  margin-bottom: 30px;\n  border-bottom: 2px solid #3498db;\n  padding-bottom: 10px;\n}\n\n.efficiency-metrics h3 {\n  color: #34495e;\n  margin-bottom: 20px;\n}\n\n/* 指标选择器样式 */\n.metrics-selector {\n  margin-bottom: 30px;\n}\n\n.selector-tabs {\n  display: flex;\n  gap: 10px;\n  border-bottom: 1px solid #e0e0e0;\n  padding-bottom: 10px;\n}\n\n.tab-btn {\n  background: #f5f5f5;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.3s ease;\n}\n\n.tab-btn.active {\n  background: #3498db;\n  color: white;\n}\n\n.tab-btn:hover:not(.active) {\n  background: #e0e0e0;\n}\n\n/* 指标卡片样式 */\n.metrics-grid {\n  display: flex;\n  flex-direction: column;\n  gap: 30px;\n}\n\n.metric-card {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n}\n\n.metric-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\n}\n\n.metric-card.level1 {\n  border-left: 4px solid #3498db;\n}\n\n.metric-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.metric-header h3,\n.metric-header h4 {\n  margin: 0;\n  color: #2c3e50;\n}\n\n.metric-value {\n  font-size: 1.5em;\n  font-weight: 700;\n  display: flex;\n  align-items: center;\n}\n\n.metric-unit {\n  font-size: 0.6em;\n  margin-left: 5px;\n  opacity: 0.7;\n}\n\n.metric-trend {\n  font-size: 0.6em;\n  margin-left: 10px;\n  padding: 3px 6px;\n  border-radius: 4px;\n}\n\n.value-good {\n  color: #2ecc71;\n}\n\n.value-warning {\n  color: #f39c12;\n}\n\n.value-danger {\n  color: #e74c3c;\n}\n\n.trend-good {\n  background: rgba(46, 204, 113, 0.2);\n  color: #27ae60;\n}\n\n.trend-bad {\n  background: rgba(231, 76, 60, 0.2);\n  color: #c0392b;\n}\n\n.trend-neutral {\n  background: rgba(149, 165, 166, 0.2);\n  color: #7f8c8d;\n}\n\n.metric-info {\n  margin-bottom: 15px;\n}\n\n.metric-desc {\n  color: #34495e;\n  margin-bottom: 5px;\n}\n\n.metric-source {\n  color: #7f8c8d;\n  font-size: 0.9em;\n}\n\n.metric-formula {\n  color: #34495e;\n  margin-bottom: 15px;\n  font-size: 0.9em;\n}\n\n.formula-label {\n  font-weight: 500;\n  color: #2c3e50;\n}\n\n/* 目标进度条样式 */\n.metric-target {\n  margin-top: 15px;\n}\n\n.target-bar {\n  height: 6px;\n  background: #ecf0f1;\n  border-radius: 3px;\n  overflow: hidden;\n  margin-bottom: 5px;\n}\n\n.target-progress {\n  height: 100%;\n  background: #3498db;\n  border-radius: 3px;\n}\n\n.target-text {\n  color: #7f8c8d;\n  font-size: 0.9em;\n}\n\n/* 二级指标样式 */\n.level2-section {\n  margin-top: 20px;\n}\n\n.level2-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 20px;\n}\n\n.metric-card.level2 {\n  border-left: 4px solid #2ecc71;\n}\n\n/* 趋势图表样式 */\n.trend-charts {\n  margin-top: 40px;\n}\n\n.chart-tabs {\n  display: flex;\n  gap: 10px;\n  margin-bottom: 20px;\n}\n\n.chart-tab {\n  background: #f5f5f5;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.3s ease;\n}\n\n.chart-tab.active {\n  background: #3498db;\n  color: white;\n}\n\n.chart-tab:hover:not(.active) {\n  background: #e0e0e0;\n}\n\n.chart-container {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n/* 模拟图表样式 */\n.chart-placeholder {\n  height: 300px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.chart-mock {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.chart-title {\n  text-align: center;\n  margin-bottom: 20px;\n  color: #2c3e50;\n  font-weight: 500;\n}\n\n.chart-mock-content {\n  flex: 1;\n  display: flex;\n  align-items: flex-end;\n  justify-content: space-around;\n  padding: 0 20px;\n  border-bottom: 1px solid #e0e0e0;\n}\n\n.mock-bar {\n  width: 40px;\n  border-radius: 4px 4px 0 0;\n  transition: height 0.5s ease;\n}\n\n.chart-mock-labels {\n  display: flex;\n  justify-content: space-around;\n  padding: 10px 20px;\n}\n\n.chart-mock-labels span {\n  color: #7f8c8d;\n  font-size: 0.9em;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .level2-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .metric-header {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n  \n  .metric-value {\n    margin-top: 10px;\n  }\n  \n  .chart-mock-content {\n    padding: 0 10px;\n  }\n  \n  .mock-bar {\n    width: 30px;\n  }\n}\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EAIxBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAe;;;;EAaWA,KAAK,EAAC;;;EACtCA,KAAK,EAAC;AAAc;;EAOhBA,KAAK,EAAC;AAAe;;EAIhBA,KAAK,EAAC;AAAa;;EASxBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAe;;EAEvBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAY;;EAMlBA,KAAK,EAAC;AAAa;;EAOvBA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAa;;EAMfA,KAAK,EAAC;AAAe;;EAIhBA,KAAK,EAAC;AAAa;;EASxBA,KAAK,EAAC;AAAgB;;EAGtBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAY;;EAMlBA,KAAK,EAAC;AAAa;;;EAWHA,KAAK,EAAC;;;EAChCA,KAAK,EAAC;AAAc;;EAOhBA,KAAK,EAAC;AAAe;;EAIhBA,KAAK,EAAC;AAAa;;EASxBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAe;;EAEvBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAY;;EAMlBA,KAAK,EAAC;AAAa;;EAOvBA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAa;;EAMfA,KAAK,EAAC;AAAe;;EAIhBA,KAAK,EAAC;AAAa;;EASxBA,KAAK,EAAC;AAAgB;;EAGtBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAY;;EAMlBA,KAAK,EAAC;AAAa;;EAW/BA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAY;;;EAWlBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAoB;;EAQ1BA,KAAK,EAAC;AAAmB;;uBA9MxCC,mBAAA,CAqNM,OArNNC,UAqNM,G,0BApNJC,mBAAA,CAAgB,YAAZ,SAAO,qBAEXC,mBAAA,WAAc,EACdD,mBAAA,CAWM,OAXNE,UAWM,GAVJF,mBAAA,CASM,OATNG,UASM,I,kBARJL,mBAAA,CAOSM,SAAA,QAAAC,WAAA,CANOC,KAAA,CAAAC,UAAU,EAAjBC,GAAG;yBADZV,mBAAA,CAOS;MALNW,GAAG,EAAED,GAAG,CAACE,EAAE;MACXb,KAAK,EAAAc,eAAA;QAAAC,MAAA,EAAwBN,KAAA,CAAAO,SAAS,KAAKL,GAAG,CAACE;MAAE;MACjDI,OAAK,EAAAC,MAAA,IAAET,KAAA,CAAAO,SAAS,GAAGL,GAAG,CAACE;wBAErBF,GAAG,CAACQ,IAAI,gCAAAC,UAAA;sCAKjBhB,mBAAA,UAAa,EACFK,KAAA,CAAAO,SAAS,qB,cAApBf,mBAAA,CA8EM,OA9ENoB,UA8EM,GA7EJlB,mBAAA,CA4EM,OA5ENmB,UA4EM,GA3EJlB,mBAAA,UAAa,G,kBACbH,mBAAA,CAiCMM,SAAA,QAAAC,WAAA,CAhCaC,KAAA,CAAAc,iBAAiB,CAACC,MAAM,EAAlCC,MAAM;yBADfxB,mBAAA,CAiCM;MA/BHW,GAAG,EAAEa,MAAM,CAACZ,EAAE;MACfb,KAAK,EAAC;QAENG,mBAAA,CAYM,OAZNuB,UAYM,GAXJvB,mBAAA,CAA0B,YAAAwB,gBAAA,CAAnBF,MAAM,CAACN,IAAI,kBAClBhB,mBAAA,CASM;MATDH,KAAK,EAAAc,eAAA,EAAC,cAAc,EAASc,QAAA,CAAAC,aAAa,CAACJ,MAAM;0CACjDG,QAAA,CAAAE,WAAW,CAACL,MAAM,CAACM,KAAK,KAAI,GAC/B,iBAAA5B,mBAAA,CAAkD,QAAlD6B,UAAkD,EAAAL,gBAAA,CAArBF,MAAM,CAACQ,IAAI,kBACxC9B,mBAAA,CAKO;MAJLH,KAAK,EAAAc,eAAA,EAAC,cAAc,EACZc,QAAA,CAAAM,aAAa,CAACT,MAAM,CAACU,KAAK;wBAE/BP,QAAA,CAAAQ,WAAW,CAACX,MAAM,CAACU,KAAK,yB,oBAIjChC,mBAAA,CAGM,OAHNkC,UAGM,GAFJlC,mBAAA,CAAuD,OAAvDmC,WAAuD,EAAAX,gBAAA,CAA3BF,MAAM,CAACc,WAAW,kBAC9CpC,mBAAA,CAA0D,OAA1DqC,WAA0D,EAA/B,QAAM,GAAAb,gBAAA,CAAGF,MAAM,CAACgB,MAAM,iB,GAEnDtC,mBAAA,CAUM,OAVNuC,WAUM,GATJvC,mBAAA,CAKM,OALNwC,WAKM,GAJJxC,mBAAA,CAGO;MAFLH,KAAK,EAAC,iBAAiB;MACtB4C,KAAK,EAAAC,eAAA;QAAAC,KAAA,EAAWlB,QAAA,CAAAmB,gBAAgB,CAACtB,MAAM;MAAA;+BAG5CtB,mBAAA,CAEM,OAFN6C,WAEM,EAFmB,QAClB,GAAArB,gBAAA,CAAGC,QAAA,CAAAE,WAAW,CAACL,MAAM,CAACwB,MAAM,KAAI,GAAC,GAAAtB,gBAAA,CAAGF,MAAM,CAACQ,IAAI,iB;kCAK1D7B,mBAAA,UAAa,EACbD,mBAAA,CAqCM,OArCN+C,WAqCM,G,0BApCJ/C,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAkCM,OAlCNgD,WAkCM,I,kBAjCJlD,mBAAA,CAgCMM,SAAA,QAAAC,WAAA,CA/BaC,KAAA,CAAAc,iBAAiB,CAAC6B,MAAM,EAAlC3B,MAAM;yBADfxB,mBAAA,CAgCM;MA9BHW,GAAG,EAAEa,MAAM,CAACZ,EAAE;MACfb,KAAK,EAAC;QAENG,mBAAA,CAYM,OAZNkD,WAYM,GAXJlD,mBAAA,CAA0B,YAAAwB,gBAAA,CAAnBF,MAAM,CAACN,IAAI,kBAClBhB,mBAAA,CASM;MATDH,KAAK,EAAAc,eAAA,EAAC,cAAc,EAASc,QAAA,CAAAC,aAAa,CAACJ,MAAM;0CACjDG,QAAA,CAAAE,WAAW,CAACL,MAAM,CAACM,KAAK,KAAI,GAC/B,iBAAA5B,mBAAA,CAAkD,QAAlDmD,WAAkD,EAAA3B,gBAAA,CAArBF,MAAM,CAACQ,IAAI,kBACxC9B,mBAAA,CAKO;MAJLH,KAAK,EAAAc,eAAA,EAAC,cAAc,EACZc,QAAA,CAAAM,aAAa,CAACT,MAAM,CAACU,KAAK;wBAE/BP,QAAA,CAAAQ,WAAW,CAACX,MAAM,CAACU,KAAK,yB,oBAIjChC,mBAAA,CAEM,OAFNoD,WAEM,G,0BADJpD,mBAAA,CAAwC;MAAlCH,KAAK,EAAC;IAAe,GAAC,OAAK,qB,iBAAO,GAAC,GAAA2B,gBAAA,CAAGF,MAAM,CAAC+B,OAAO,iB,GAE5DrD,mBAAA,CAUM,OAVNsD,WAUM,GATJtD,mBAAA,CAKM,OALNuD,WAKM,GAJJvD,mBAAA,CAGO;MAFLH,KAAK,EAAC,iBAAiB;MACtB4C,KAAK,EAAAC,eAAA;QAAAC,KAAA,EAAWlB,QAAA,CAAAmB,gBAAgB,CAACtB,MAAM;MAAA;+BAG5CtB,mBAAA,CAEM,OAFNwD,WAEM,EAFmB,QAClB,GAAAhC,gBAAA,CAAGC,QAAA,CAAAE,WAAW,CAACL,MAAM,CAACwB,MAAM,KAAI,GAAC,GAAAtB,gBAAA,CAAGF,MAAM,CAACQ,IAAI,iB;+EASlE7B,mBAAA,UAAa,EACFK,KAAA,CAAAO,SAAS,e,cAApBf,mBAAA,CA8EM,OA9EN2D,WA8EM,GA7EJzD,mBAAA,CA4EM,OA5EN0D,WA4EM,GA3EJzD,mBAAA,UAAa,G,kBACbH,mBAAA,CAiCMM,SAAA,QAAAC,WAAA,CAhCaC,KAAA,CAAAqD,WAAW,CAACtC,MAAM,EAA5BC,MAAM;yBADfxB,mBAAA,CAiCM;MA/BHW,GAAG,EAAEa,MAAM,CAACZ,EAAE;MACfb,KAAK,EAAC;QAENG,mBAAA,CAYM,OAZN4D,WAYM,GAXJ5D,mBAAA,CAA0B,YAAAwB,gBAAA,CAAnBF,MAAM,CAACN,IAAI,kBAClBhB,mBAAA,CASM;MATDH,KAAK,EAAAc,eAAA,EAAC,cAAc,EAASc,QAAA,CAAAC,aAAa,CAACJ,MAAM;0CACjDG,QAAA,CAAAE,WAAW,CAACL,MAAM,CAACM,KAAK,KAAI,GAC/B,iBAAA5B,mBAAA,CAAkD,QAAlD6D,WAAkD,EAAArC,gBAAA,CAArBF,MAAM,CAACQ,IAAI,kBACxC9B,mBAAA,CAKO;MAJLH,KAAK,EAAAc,eAAA,EAAC,cAAc,EACZc,QAAA,CAAAM,aAAa,CAACT,MAAM,CAACU,KAAK;wBAE/BP,QAAA,CAAAQ,WAAW,CAACX,MAAM,CAACU,KAAK,yB,oBAIjChC,mBAAA,CAGM,OAHN8D,WAGM,GAFJ9D,mBAAA,CAAuD,OAAvD+D,WAAuD,EAAAvC,gBAAA,CAA3BF,MAAM,CAACc,WAAW,kBAC9CpC,mBAAA,CAA0D,OAA1DgE,WAA0D,EAA/B,QAAM,GAAAxC,gBAAA,CAAGF,MAAM,CAACgB,MAAM,iB,GAEnDtC,mBAAA,CAUM,OAVNiE,WAUM,GATJjE,mBAAA,CAKM,OALNkE,WAKM,GAJJlE,mBAAA,CAGO;MAFLH,KAAK,EAAC,iBAAiB;MACtB4C,KAAK,EAAAC,eAAA;QAAAC,KAAA,EAAWlB,QAAA,CAAAmB,gBAAgB,CAACtB,MAAM;MAAA;+BAG5CtB,mBAAA,CAEM,OAFNmE,WAEM,EAFmB,QAClB,GAAA3C,gBAAA,CAAGC,QAAA,CAAAE,WAAW,CAACL,MAAM,CAACwB,MAAM,KAAI,GAAC,GAAAtB,gBAAA,CAAGF,MAAM,CAACQ,IAAI,iB;kCAK1D7B,mBAAA,UAAa,EACbD,mBAAA,CAqCM,OArCNoE,WAqCM,G,0BApCJpE,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAkCM,OAlCNqE,WAkCM,I,kBAjCJvE,mBAAA,CAgCMM,SAAA,QAAAC,WAAA,CA/BaC,KAAA,CAAAqD,WAAW,CAACV,MAAM,EAA5B3B,MAAM;yBADfxB,mBAAA,CAgCM;MA9BHW,GAAG,EAAEa,MAAM,CAACZ,EAAE;MACfb,KAAK,EAAC;QAENG,mBAAA,CAYM,OAZNsE,WAYM,GAXJtE,mBAAA,CAA0B,YAAAwB,gBAAA,CAAnBF,MAAM,CAACN,IAAI,kBAClBhB,mBAAA,CASM;MATDH,KAAK,EAAAc,eAAA,EAAC,cAAc,EAASc,QAAA,CAAAC,aAAa,CAACJ,MAAM;0CACjDG,QAAA,CAAAE,WAAW,CAACL,MAAM,CAACM,KAAK,KAAI,GAC/B,iBAAA5B,mBAAA,CAAkD,QAAlDuE,WAAkD,EAAA/C,gBAAA,CAArBF,MAAM,CAACQ,IAAI,kBACxC9B,mBAAA,CAKO;MAJLH,KAAK,EAAAc,eAAA,EAAC,cAAc,EACZc,QAAA,CAAAM,aAAa,CAACT,MAAM,CAACU,KAAK;wBAE/BP,QAAA,CAAAQ,WAAW,CAACX,MAAM,CAACU,KAAK,yB,oBAIjChC,mBAAA,CAEM,OAFNwE,WAEM,G,0BADJxE,mBAAA,CAAwC;MAAlCH,KAAK,EAAC;IAAe,GAAC,OAAK,qB,iBAAO,GAAC,GAAA2B,gBAAA,CAAGF,MAAM,CAAC+B,OAAO,iB,GAE5DrD,mBAAA,CAUM,OAVNyE,WAUM,GATJzE,mBAAA,CAKM,OALN0E,WAKM,GAJJ1E,mBAAA,CAGO;MAFLH,KAAK,EAAC,iBAAiB;MACtB4C,KAAK,EAAAC,eAAA;QAAAC,KAAA,EAAWlB,QAAA,CAAAmB,gBAAgB,CAACtB,MAAM;MAAA;+BAG5CtB,mBAAA,CAEM,OAFN2E,WAEM,EAFmB,QAClB,GAAAnD,gBAAA,CAAGC,QAAA,CAAAE,WAAW,CAACL,MAAM,CAACwB,MAAM,KAAI,GAAC,GAAAtB,gBAAA,CAAGF,MAAM,CAACQ,IAAI,iB;+EASlE7B,mBAAA,UAAa,EACbD,mBAAA,CAgCM,OAhCN4E,WAgCM,G,0BA/BJ5E,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CASM,OATN6E,WASM,I,kBARJ/E,mBAAA,CAOSM,SAAA,QAAAC,WAAA,CANSC,KAAA,CAAAwE,YAAY,EAArBC,KAAK;yBADdjF,mBAAA,CAOS;MALNW,GAAG,EAAEsE,KAAK,CAACrE,EAAE;MACbb,KAAK,EAAAc,eAAA;QAAAC,MAAA,EAA0BN,KAAA,CAAA0E,WAAW,KAAKD,KAAK,CAACrE;MAAE;MACvDI,OAAK,EAAAC,MAAA,IAAET,KAAA,CAAA0E,WAAW,GAAGD,KAAK,CAACrE;wBAEzBqE,KAAK,CAAC/D,IAAI,gCAAAiE,WAAA;oCAIjBjF,mBAAA,CAkBM,OAlBNkF,WAkBM,GAjBJjF,mBAAA,iCAAoC,EACpCD,mBAAA,CAeM,OAfNmF,WAeM,GAdJnF,mBAAA,CAaM,OAbNoF,WAaM,GAZJpF,mBAAA,CAA0D,OAA1DqF,WAA0D,EAAA7D,gBAAA,CAA9BC,QAAA,CAAA6D,mBAAmB,oBAC/CtF,mBAAA,CAOM,OAPNuF,WAOM,I,kBANJzF,mBAAA,CAKOM,SAAA,QAAAC,WAAA,CAJkBoB,QAAA,CAAA+D,gBAAgB,KAA/BC,GAAG,EAAEC,KAAK;yBADpB5F,mBAAA,CAKO;MAHJW,GAAG,EAAEiF,KAAK;MACX7F,KAAK,EAAC,UAAU;MACf4C,KAAK,EAAAC,eAAA;QAAAiD,MAAA,EAAYF,GAAG,CAACE,MAAM;QAAAC,eAAA,EAAmBH,GAAG,CAACI;MAAK;;oCAG5D7F,mBAAA,CAEM,OAFN8F,WAEM,I,kBADJhG,mBAAA,CAAsEM,SAAA,QAAAC,WAAA,CAAvCC,KAAA,CAAAyF,MAAM,GAAvBC,KAAK,EAAEN,KAAK;yBAA1B5F,mBAAA,CAAsE;MAA9BW,GAAG,EAAEiF;IAAK,GAAAlE,gBAAA,CAAKwE,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}