{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, vModelText as _vModelText, withDirectives as _withDirectives, withModifiers as _withModifiers, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, vModelSelect as _vModelSelect, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"data-collection\"\n};\nconst _hoisted_2 = {\n  class: \"scenario-creation\"\n};\nconst _hoisted_3 = {\n  class: \"form-card\"\n};\nconst _hoisted_4 = {\n  class: \"form-group\"\n};\nconst _hoisted_5 = {\n  class: \"form-group\"\n};\nconst _hoisted_6 = {\n  class: \"form-group\"\n};\nconst _hoisted_7 = {\n  class: \"form-group\"\n};\nconst _hoisted_8 = {\n  class: \"stage-collection\"\n};\nconst _hoisted_9 = {\n  class: \"form-card\"\n};\nconst _hoisted_10 = {\n  class: \"form-row\"\n};\nconst _hoisted_11 = {\n  class: \"form-group\"\n};\nconst _hoisted_12 = {\n  class: \"form-group\"\n};\nconst _hoisted_13 = [\"value\"];\nconst _hoisted_14 = {\n  class: \"form-row\"\n};\nconst _hoisted_15 = {\n  class: \"form-group\"\n};\nconst _hoisted_16 = {\n  class: \"form-group\"\n};\nconst _hoisted_17 = {\n  class: \"steps-section\"\n};\nconst _hoisted_18 = {\n  class: \"steps-grid\"\n};\nconst _hoisted_19 = [\"onUpdate:modelValue\"];\nconst _hoisted_20 = [\"onUpdate:modelValue\"];\nconst _hoisted_21 = [\"onClick\"];\nconst _hoisted_22 = {\n  class: \"form-group\"\n};\nconst _hoisted_23 = {\n  class: \"calculated-value\"\n};\nconst _hoisted_24 = {\n  class: \"data-preview\"\n};\nconst _hoisted_25 = {\n  class: \"preview-section\"\n};\nconst _hoisted_26 = {\n  class: \"scenario-list\"\n};\nconst _hoisted_27 = {\n  class: \"scenario-info\"\n};\nconst _hoisted_28 = {\n  class: \"scenario-id\"\n};\nconst _hoisted_29 = {\n  class: \"scenario-owner\"\n};\nconst _hoisted_30 = {\n  class: \"scenario-desc\"\n};\nconst _hoisted_31 = {\n  class: \"scenario-meta\"\n};\nconst _hoisted_32 = {\n  class: \"created-time\"\n};\nconst _hoisted_33 = {\n  class: \"preview-section\"\n};\nconst _hoisted_34 = {\n  class: \"stage-list\"\n};\nconst _hoisted_35 = {\n  class: \"stage-header\"\n};\nconst _hoisted_36 = {\n  class: \"stage-scenario\"\n};\nconst _hoisted_37 = {\n  class: \"stage-duration\"\n};\nconst _hoisted_38 = {\n  class: \"stage-steps\"\n};\nconst _hoisted_39 = {\n  class: \"stage-time\"\n};\nconst _hoisted_40 = {\n  class: \"json-preview\"\n};\nconst _hoisted_41 = {\n  class: \"json-tabs\"\n};\nconst _hoisted_42 = {\n  class: \"json-content\"\n};\nconst _hoisted_43 = {\n  key: 0\n};\nconst _hoisted_44 = {\n  key: 1\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[32] || (_cache[32] = _createElementVNode(\"h2\", null, \"数据采集与上报\", -1 /* CACHED */)), _createCommentVNode(\" 场景创建表单 \"), _createElementVNode(\"div\", _hoisted_2, [_cache[18] || (_cache[18] = _createElementVNode(\"h3\", null, \"场景创建\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"form\", {\n    onSubmit: _cache[4] || (_cache[4] = _withModifiers((...args) => $options.createScenario && $options.createScenario(...args), [\"prevent\"]))\n  }, [_createElementVNode(\"div\", _hoisted_4, [_cache[13] || (_cache[13] = _createElementVNode(\"label\", {\n    for: \"scenarioId\"\n  }, \"场景唯一标识\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    id: \"scenarioId\",\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $data.scenarioForm.scenario_id = $event),\n    placeholder: \"例如: proj_abc\",\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.scenarioForm.scenario_id]])]), _createElementVNode(\"div\", _hoisted_5, [_cache[14] || (_cache[14] = _createElementVNode(\"label\", {\n    for: \"scenarioName\"\n  }, \"场景名称\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    id: \"scenarioName\",\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $data.scenarioForm.scenario_name = $event),\n    placeholder: \"例如: 组件库优化\",\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.scenarioForm.scenario_name]])]), _createElementVNode(\"div\", _hoisted_6, [_cache[15] || (_cache[15] = _createElementVNode(\"label\", {\n    for: \"scenarioOwner\"\n  }, \"场景负责人\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    id: \"scenarioOwner\",\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.scenarioForm.scenario_owner = $event),\n    placeholder: \"例如: 李四\",\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.scenarioForm.scenario_owner]])]), _createElementVNode(\"div\", _hoisted_7, [_cache[16] || (_cache[16] = _createElementVNode(\"label\", {\n    for: \"scenarioDesc\"\n  }, \"场景描述\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"textarea\", {\n    id: \"scenarioDesc\",\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $data.scenarioForm.scenario_desc = $event),\n    placeholder: \"例如: 组件库性能与复用优化\",\n    rows: \"3\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.scenarioForm.scenario_desc]])]), _cache[17] || (_cache[17] = _createElementVNode(\"button\", {\n    type: \"submit\",\n    class: \"btn-primary\"\n  }, \"创建场景\", -1 /* CACHED */))], 32 /* NEED_HYDRATION */)])]), _createCommentVNode(\" 阶段耗时采集 \"), _createElementVNode(\"div\", _hoisted_8, [_cache[27] || (_cache[27] = _createElementVNode(\"h3\", null, \"阶段耗时采集\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"form\", {\n    onSubmit: _cache[10] || (_cache[10] = _withModifiers((...args) => $options.submitStageData && $options.submitStageData(...args), [\"prevent\"]))\n  }, [_createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_cache[19] || (_cache[19] = _createElementVNode(\"label\", {\n    for: \"executionId\"\n  }, \"执行记录ID\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    id: \"executionId\",\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.stageForm.execution_id = $event),\n    placeholder: \"例如: build_20240601_001\",\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.stageForm.execution_id]])]), _createElementVNode(\"div\", _hoisted_12, [_cache[21] || (_cache[21] = _createElementVNode(\"label\", {\n    for: \"relatedScenario\"\n  }, \"关联场景\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"select\", {\n    id: \"relatedScenario\",\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.stageForm.scenario_id = $event),\n    required: \"\"\n  }, [_cache[20] || (_cache[20] = _createElementVNode(\"option\", {\n    value: \"\"\n  }, \"请选择场景\", -1 /* CACHED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.scenarios, scenario => {\n    return _openBlock(), _createElementBlock(\"option\", {\n      key: scenario.scenario_id,\n      value: scenario.scenario_id\n    }, _toDisplayString(scenario.scenario_name), 9 /* TEXT, PROPS */, _hoisted_13);\n  }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.stageForm.scenario_id]])])]), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_cache[22] || (_cache[22] = _createElementVNode(\"label\", {\n    for: \"startTime\"\n  }, \"开始时间\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"datetime-local\",\n    id: \"startTime\",\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.stageForm.started_at = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.stageForm.started_at]])]), _createElementVNode(\"div\", _hoisted_16, [_cache[23] || (_cache[23] = _createElementVNode(\"label\", {\n    for: \"endTime\"\n  }, \"结束时间\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"datetime-local\",\n    id: \"endTime\",\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.stageForm.ended_at = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.stageForm.ended_at]])])]), _createCommentVNode(\" 步骤耗时明细 \"), _createElementVNode(\"div\", _hoisted_17, [_cache[24] || (_cache[24] = _createElementVNode(\"label\", null, \"步骤耗时明细（分钟）\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_18, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.stageForm.step_durations, (step, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      class: \"step-item\"\n    }, [_withDirectives(_createElementVNode(\"input\", {\n      type: \"text\",\n      \"onUpdate:modelValue\": $event => step.name = $event,\n      placeholder: \"步骤名称\",\n      class: \"step-name\"\n    }, null, 8 /* PROPS */, _hoisted_19), [[_vModelText, step.name]]), _withDirectives(_createElementVNode(\"input\", {\n      type: \"number\",\n      \"onUpdate:modelValue\": $event => step.duration = $event,\n      placeholder: \"耗时\",\n      step: \"0.1\",\n      min: \"0\",\n      class: \"step-duration\"\n    }, null, 8 /* PROPS */, _hoisted_20), [[_vModelText, step.duration, void 0, {\n      number: true\n    }]]), $data.stageForm.step_durations.length > 1 ? (_openBlock(), _createElementBlock(\"button\", {\n      key: 0,\n      type: \"button\",\n      onClick: $event => $options.removeStep(index),\n      class: \"btn-remove\"\n    }, \" × \", 8 /* PROPS */, _hoisted_21)) : _createCommentVNode(\"v-if\", true)]);\n  }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"button\", {\n    type: \"button\",\n    onClick: _cache[9] || (_cache[9] = (...args) => $options.addStep && $options.addStep(...args)),\n    class: \"btn-secondary\"\n  }, \"添加步骤\")]), _createElementVNode(\"div\", _hoisted_22, [_cache[25] || (_cache[25] = _createElementVNode(\"label\", null, \"总耗时（自动计算）\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_23, _toDisplayString($options.totalDuration.toFixed(1)) + \" 分钟\", 1 /* TEXT */)]), _cache[26] || (_cache[26] = _createElementVNode(\"button\", {\n    type: \"submit\",\n    class: \"btn-primary\"\n  }, \"提交阶段数据\", -1 /* CACHED */))], 32 /* NEED_HYDRATION */)])]), _createCommentVNode(\" 数据预览 \"), _createElementVNode(\"div\", _hoisted_24, [_cache[30] || (_cache[30] = _createElementVNode(\"h3\", null, \"数据预览\", -1 /* CACHED */)), _createCommentVNode(\" 场景列表 \"), _createElementVNode(\"div\", _hoisted_25, [_cache[28] || (_cache[28] = _createElementVNode(\"h4\", null, \"已创建场景\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_26, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.scenarios, scenario => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: scenario.scenario_id,\n      class: \"scenario-item\"\n    }, [_createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"strong\", null, _toDisplayString(scenario.scenario_name), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_28, \"ID: \" + _toDisplayString(scenario.scenario_id), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_29, \"负责人: \" + _toDisplayString(scenario.scenario_owner), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_30, _toDisplayString(scenario.scenario_desc), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"span\", _hoisted_32, _toDisplayString($options.formatTime(scenario.created_at)), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 阶段数据列表 \"), _createElementVNode(\"div\", _hoisted_33, [_cache[29] || (_cache[29] = _createElementVNode(\"h4\", null, \"阶段耗时记录\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_34, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.stageRecords, stage => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: stage.execution_id,\n      class: \"stage-item\"\n    }, [_createElementVNode(\"div\", _hoisted_35, [_createElementVNode(\"strong\", null, _toDisplayString(stage.execution_id), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_36, \"场景: \" + _toDisplayString($options.getScenarioName(stage.scenario_id)), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_37, \"总耗时: \" + _toDisplayString(stage.total_duration_minutes) + \"分钟\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_38, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(stage.step_durations, (duration, stepName) => {\n      return _openBlock(), _createElementBlock(\"span\", {\n        key: stepName,\n        class: \"step-tag\"\n      }, _toDisplayString(stepName) + \": \" + _toDisplayString(duration) + \"分钟 \", 1 /* TEXT */);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"div\", _hoisted_39, _toDisplayString($options.formatTime(stage.started_at)) + \" - \" + _toDisplayString($options.formatTime(stage.ended_at)), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))])])]), _createCommentVNode(\" JSON数据展示 \"), _createElementVNode(\"div\", _hoisted_40, [_cache[31] || (_cache[31] = _createElementVNode(\"h3\", null, \"JSON数据格式\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_41, [_createElementVNode(\"button\", {\n    class: _normalizeClass(['tab-btn', {\n      active: $data.activeTab === 'scenario'\n    }]),\n    onClick: _cache[11] || (_cache[11] = $event => $data.activeTab = 'scenario')\n  }, \" 场景创建 \", 2 /* CLASS */), _createElementVNode(\"button\", {\n    class: _normalizeClass(['tab-btn', {\n      active: $data.activeTab === 'stage'\n    }]),\n    onClick: _cache[12] || (_cache[12] = $event => $data.activeTab = 'stage')\n  }, \" 阶段耗时 \", 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_42, [$data.activeTab === 'scenario' ? (_openBlock(), _createElementBlock(\"pre\", _hoisted_43, _toDisplayString($options.scenarioJsonExample), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), $data.activeTab === 'stage' ? (_openBlock(), _createElementBlock(\"pre\", _hoisted_44, _toDisplayString($options.stageJsonExample), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createCommentVNode", "_hoisted_2", "_hoisted_3", "onSubmit", "_cache", "_withModifiers", "args", "$options", "createScenario", "_hoisted_4", "for", "type", "id", "$data", "scenarioForm", "scenario_id", "$event", "placeholder", "required", "_hoisted_5", "scenario_name", "_hoisted_6", "scenario_owner", "_hoisted_7", "scenario_desc", "rows", "_hoisted_8", "_hoisted_9", "submitStageData", "_hoisted_10", "_hoisted_11", "stageForm", "execution_id", "_hoisted_12", "value", "_Fragment", "_renderList", "scenarios", "scenario", "key", "_hoisted_13", "_hoisted_14", "_hoisted_15", "started_at", "_hoisted_16", "ended_at", "_hoisted_17", "_hoisted_18", "step_durations", "step", "index", "name", "duration", "min", "number", "length", "onClick", "removeStep", "_hoisted_21", "addStep", "_hoisted_22", "_hoisted_23", "_toDisplayString", "totalDuration", "toFixed", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_32", "formatTime", "created_at", "_hoisted_33", "_hoisted_34", "stageRecords", "stage", "_hoisted_35", "_hoisted_36", "getScenarioName", "_hoisted_37", "total_duration_minutes", "_hoisted_38", "<PERSON><PERSON><PERSON>", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_normalizeClass", "active", "activeTab", "_hoisted_42", "_hoisted_43", "scenario<PERSON><PERSON>Example", "_hoisted_44", "stage<PERSON>sonExample"], "sources": ["/Users/<USER>/Desktop/dev/frontend-project/src/components/DataCollection.vue"], "sourcesContent": ["<template>\n  <div class=\"data-collection\">\n    <h2>数据采集与上报</h2>\n    \n    <!-- 场景创建表单 -->\n    <div class=\"scenario-creation\">\n      <h3>场景创建</h3>\n      <div class=\"form-card\">\n        <form @submit.prevent=\"createScenario\">\n          <div class=\"form-group\">\n            <label for=\"scenarioId\">场景唯一标识</label>\n            <input \n              type=\"text\" \n              id=\"scenarioId\" \n              v-model=\"scenarioForm.scenario_id\" \n              placeholder=\"例如: proj_abc\"\n              required\n            >\n          </div>\n          \n          <div class=\"form-group\">\n            <label for=\"scenarioName\">场景名称</label>\n            <input \n              type=\"text\" \n              id=\"scenarioName\" \n              v-model=\"scenarioForm.scenario_name\" \n              placeholder=\"例如: 组件库优化\"\n              required\n            >\n          </div>\n          \n          <div class=\"form-group\">\n            <label for=\"scenarioOwner\">场景负责人</label>\n            <input \n              type=\"text\" \n              id=\"scenarioOwner\" \n              v-model=\"scenarioForm.scenario_owner\" \n              placeholder=\"例如: 李四\"\n              required\n            >\n          </div>\n          \n          <div class=\"form-group\">\n            <label for=\"scenarioDesc\">场景描述</label>\n            <textarea \n              id=\"scenarioDesc\" \n              v-model=\"scenarioForm.scenario_desc\" \n              placeholder=\"例如: 组件库性能与复用优化\"\n              rows=\"3\"\n            ></textarea>\n          </div>\n          \n          <button type=\"submit\" class=\"btn-primary\">创建场景</button>\n        </form>\n      </div>\n    </div>\n    \n    <!-- 阶段耗时采集 -->\n    <div class=\"stage-collection\">\n      <h3>阶段耗时采集</h3>\n      <div class=\"form-card\">\n        <form @submit.prevent=\"submitStageData\">\n          <div class=\"form-row\">\n            <div class=\"form-group\">\n              <label for=\"executionId\">执行记录ID</label>\n              <input \n                type=\"text\" \n                id=\"executionId\" \n                v-model=\"stageForm.execution_id\" \n                placeholder=\"例如: build_20240601_001\"\n                required\n              >\n            </div>\n            \n            <div class=\"form-group\">\n              <label for=\"relatedScenario\">关联场景</label>\n              <select id=\"relatedScenario\" v-model=\"stageForm.scenario_id\" required>\n                <option value=\"\">请选择场景</option>\n                <option \n                  v-for=\"scenario in scenarios\" \n                  :key=\"scenario.scenario_id\" \n                  :value=\"scenario.scenario_id\"\n                >\n                  {{ scenario.scenario_name }}\n                </option>\n              </select>\n            </div>\n          </div>\n          \n          <div class=\"form-row\">\n            <div class=\"form-group\">\n              <label for=\"startTime\">开始时间</label>\n              <input \n                type=\"datetime-local\" \n                id=\"startTime\" \n                v-model=\"stageForm.started_at\" \n                required\n              >\n            </div>\n            \n            <div class=\"form-group\">\n              <label for=\"endTime\">结束时间</label>\n              <input \n                type=\"datetime-local\" \n                id=\"endTime\" \n                v-model=\"stageForm.ended_at\" \n                required\n              >\n            </div>\n          </div>\n          \n          <!-- 步骤耗时明细 -->\n          <div class=\"steps-section\">\n            <label>步骤耗时明细（分钟）</label>\n            <div class=\"steps-grid\">\n              <div \n                v-for=\"(step, index) in stageForm.step_durations\" \n                :key=\"index\" \n                class=\"step-item\"\n              >\n                <input \n                  type=\"text\" \n                  v-model=\"step.name\" \n                  placeholder=\"步骤名称\"\n                  class=\"step-name\"\n                >\n                <input \n                  type=\"number\" \n                  v-model.number=\"step.duration\" \n                  placeholder=\"耗时\"\n                  step=\"0.1\"\n                  min=\"0\"\n                  class=\"step-duration\"\n                >\n                <button \n                  type=\"button\" \n                  @click=\"removeStep(index)\" \n                  class=\"btn-remove\"\n                  v-if=\"stageForm.step_durations.length > 1\"\n                >\n                  ×\n                </button>\n              </div>\n            </div>\n            <button type=\"button\" @click=\"addStep\" class=\"btn-secondary\">添加步骤</button>\n          </div>\n          \n          <div class=\"form-group\">\n            <label>总耗时（自动计算）</label>\n            <div class=\"calculated-value\">{{ totalDuration.toFixed(1) }} 分钟</div>\n          </div>\n          \n          <button type=\"submit\" class=\"btn-primary\">提交阶段数据</button>\n        </form>\n      </div>\n    </div>\n    \n    <!-- 数据预览 -->\n    <div class=\"data-preview\">\n      <h3>数据预览</h3>\n      \n      <!-- 场景列表 -->\n      <div class=\"preview-section\">\n        <h4>已创建场景</h4>\n        <div class=\"scenario-list\">\n          <div \n            v-for=\"scenario in scenarios\" \n            :key=\"scenario.scenario_id\" \n            class=\"scenario-item\"\n          >\n            <div class=\"scenario-info\">\n              <strong>{{ scenario.scenario_name }}</strong>\n              <span class=\"scenario-id\">ID: {{ scenario.scenario_id }}</span>\n              <span class=\"scenario-owner\">负责人: {{ scenario.scenario_owner }}</span>\n              <p class=\"scenario-desc\">{{ scenario.scenario_desc }}</p>\n            </div>\n            <div class=\"scenario-meta\">\n              <span class=\"created-time\">{{ formatTime(scenario.created_at) }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 阶段数据列表 -->\n      <div class=\"preview-section\">\n        <h4>阶段耗时记录</h4>\n        <div class=\"stage-list\">\n          <div \n            v-for=\"stage in stageRecords\" \n            :key=\"stage.execution_id\" \n            class=\"stage-item\"\n          >\n            <div class=\"stage-header\">\n              <strong>{{ stage.execution_id }}</strong>\n              <span class=\"stage-scenario\">场景: {{ getScenarioName(stage.scenario_id) }}</span>\n              <span class=\"stage-duration\">总耗时: {{ stage.total_duration_minutes }}分钟</span>\n            </div>\n            <div class=\"stage-steps\">\n              <span \n                v-for=\"(duration, stepName) in stage.step_durations\" \n                :key=\"stepName\" \n                class=\"step-tag\"\n              >\n                {{ stepName }}: {{ duration }}分钟\n              </span>\n            </div>\n            <div class=\"stage-time\">\n              {{ formatTime(stage.started_at) }} - {{ formatTime(stage.ended_at) }}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- JSON数据展示 -->\n    <div class=\"json-preview\">\n      <h3>JSON数据格式</h3>\n      <div class=\"json-tabs\">\n        <button \n          :class=\"['tab-btn', { active: activeTab === 'scenario' }]\" \n          @click=\"activeTab = 'scenario'\"\n        >\n          场景创建\n        </button>\n        <button \n          :class=\"['tab-btn', { active: activeTab === 'stage' }]\" \n          @click=\"activeTab = 'stage'\"\n        >\n          阶段耗时\n        </button>\n      </div>\n      \n      <div class=\"json-content\">\n        <pre v-if=\"activeTab === 'scenario'\">{{ scenarioJsonExample }}</pre>\n        <pre v-if=\"activeTab === 'stage'\">{{ stageJsonExample }}</pre>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'DataCollection',\n  data() {\n    return {\n      activeTab: 'scenario',\n      \n      // 场景创建表单\n      scenarioForm: {\n        scenario_id: '',\n        scenario_name: '',\n        scenario_owner: '',\n        scenario_desc: ''\n      },\n      \n      // 阶段耗时表单\n      stageForm: {\n        execution_id: '',\n        scenario_id: '',\n        started_at: '',\n        ended_at: '',\n        step_durations: [\n          { name: '依赖安装', duration: 0 },\n          { name: '编译', duration: 0 },\n          { name: '打包', duration: 0 }\n        ]\n      },\n      \n      // 数据存储\n      scenarios: [\n        {\n          scenario_id: 'proj_abc',\n          scenario_name: '组件库优化',\n          scenario_owner: '李四',\n          created_at: '2024-06-01T09:00:00Z',\n          scenario_desc: '组件库性能与复用优化'\n        }\n      ],\n      \n      stageRecords: [\n        {\n          execution_id: 'build_20240601_001',\n          scenario_id: 'proj_abc',\n          started_at: '2024-06-01T10:00:00Z',\n          ended_at: '2024-06-01T10:05:00Z',\n          step_durations: {\n            '依赖安装': 1.5,\n            '编译': 2.0,\n            '打包': 1.5\n          },\n          total_duration_minutes: 5.0\n        }\n      ]\n    }\n  },\n  \n  computed: {\n    totalDuration() {\n      return this.stageForm.step_durations.reduce((total, step) => {\n        return total + (step.duration || 0)\n      }, 0)\n    },\n    \n    scenarioJsonExample() {\n      return JSON.stringify({\n        scenario_id: 'proj_abc',\n        scenario_name: '组件库优化',\n        scenario_owner: '李四',\n        created_at: '2024-06-01T09:00:00Z',\n        scenario_desc: '组件库性能与复用优化'\n      }, null, 2)\n    },\n    \n    stageJsonExample() {\n      return JSON.stringify({\n        execution_id: 'build_20240601_001',\n        scenario_id: 'proj_abc',\n        started_at: '2024-06-01T10:00:00Z',\n        ended_at: '2024-06-01T10:05:00Z',\n        step_durations: {\n          '依赖安装': 1.5,\n          '编译': 2.0,\n          '打包': 1.5\n        },\n        total_duration_minutes: 5.0\n      }, null, 2)\n    }\n  },\n  \n  methods: {\n    createScenario() {\n      const newScenario = {\n        ...this.scenarioForm,\n        created_at: new Date().toISOString()\n      }\n      \n      this.scenarios.push(newScenario)\n      \n      // 重置表单\n      this.scenarioForm = {\n        scenario_id: '',\n        scenario_name: '',\n        scenario_owner: '',\n        scenario_desc: ''\n      }\n      \n      alert('场景创建成功！')\n    },\n    \n    submitStageData() {\n      // 转换步骤数据格式\n      const stepDurations = {}\n      this.stageForm.step_durations.forEach(step => {\n        if (step.name && step.duration) {\n          stepDurations[step.name] = step.duration\n        }\n      })\n      \n      const newStageRecord = {\n        execution_id: this.stageForm.execution_id,\n        scenario_id: this.stageForm.scenario_id,\n        started_at: this.stageForm.started_at,\n        ended_at: this.stageForm.ended_at,\n        step_durations: stepDurations,\n        total_duration_minutes: this.totalDuration\n      }\n      \n      this.stageRecords.push(newStageRecord)\n      \n      // 重置表单\n      this.stageForm = {\n        execution_id: '',\n        scenario_id: '',\n        started_at: '',\n        ended_at: '',\n        step_durations: [\n          { name: '依赖安装', duration: 0 },\n          { name: '编译', duration: 0 },\n          { name: '打包', duration: 0 }\n        ]\n      }\n      \n      alert('阶段数据提交成功！')\n    },\n    \n    addStep() {\n      this.stageForm.step_durations.push({ name: '', duration: 0 })\n    },\n    \n    removeStep(index) {\n      this.stageForm.step_durations.splice(index, 1)\n    },\n    \n    getScenarioName(scenarioId) {\n      const scenario = this.scenarios.find(s => s.scenario_id === scenarioId)\n      return scenario ? scenario.scenario_name : scenarioId\n    },\n    \n    formatTime(timeString) {\n      return new Date(timeString).toLocaleString('zh-CN')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.data-collection {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.data-collection h2 {\n  color: #2c3e50;\n  margin-bottom: 30px;\n  border-bottom: 2px solid #3498db;\n  padding-bottom: 10px;\n}\n\n.data-collection h3 {\n  color: #34495e;\n  margin-bottom: 20px;\n}\n\n.data-collection h4 {\n  color: #2c3e50;\n  margin-bottom: 15px;\n  font-size: 1.1em;\n}\n\n/* 表单样式 */\n.form-card {\n  background: white;\n  border-radius: 8px;\n  padding: 24px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  margin-bottom: 30px;\n}\n\n.form-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n}\n\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 8px;\n  font-weight: 500;\n  color: #2c3e50;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  width: 100%;\n  padding: 10px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n  transition: border-color 0.3s;\n}\n\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #3498db;\n}\n\n/* 步骤耗时样式 */\n.steps-section {\n  margin-bottom: 20px;\n}\n\n.steps-section label {\n  display: block;\n  margin-bottom: 12px;\n  font-weight: 500;\n  color: #2c3e50;\n}\n\n.steps-grid {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n  margin-bottom: 15px;\n}\n\n.step-item {\n  display: flex;\n  gap: 10px;\n  align-items: center;\n}\n\n.step-name {\n  flex: 2;\n}\n\n.step-duration {\n  flex: 1;\n}\n\n.btn-remove {\n  background: #e74c3c;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  width: 30px;\n  height: 30px;\n  cursor: pointer;\n  font-size: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-remove:hover {\n  background: #c0392b;\n}\n\n.calculated-value {\n  background: #f8f9fa;\n  padding: 10px;\n  border-radius: 4px;\n  font-weight: 500;\n  color: #2c3e50;\n}\n\n/* 按钮样式 */\n.btn-primary {\n  background: #3498db;\n  color: white;\n  border: none;\n  padding: 12px 24px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  font-weight: 500;\n  transition: background 0.3s;\n}\n\n.btn-primary:hover {\n  background: #2980b9;\n}\n\n.btn-secondary {\n  background: #95a5a6;\n  color: white;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: background 0.3s;\n}\n\n.btn-secondary:hover {\n  background: #7f8c8d;\n}\n\n/* 预览区域样式 */\n.data-preview {\n  margin-top: 40px;\n}\n\n.preview-section {\n  margin-bottom: 30px;\n}\n\n.scenario-list,\n.stage-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.scenario-item,\n.stage-item {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  border-left: 4px solid #3498db;\n}\n\n.scenario-info {\n  margin-bottom: 10px;\n}\n\n.scenario-info strong {\n  color: #2c3e50;\n  font-size: 1.1em;\n}\n\n.scenario-id,\n.scenario-owner {\n  display: inline-block;\n  margin-left: 15px;\n  color: #7f8c8d;\n  font-size: 0.9em;\n}\n\n.scenario-desc {\n  margin: 8px 0 0 0;\n  color: #34495e;\n}\n\n.scenario-meta,\n.stage-time {\n  color: #95a5a6;\n  font-size: 0.9em;\n}\n\n.stage-header {\n  margin-bottom: 10px;\n}\n\n.stage-header strong {\n  color: #2c3e50;\n  margin-right: 15px;\n}\n\n.stage-scenario,\n.stage-duration {\n  color: #7f8c8d;\n  font-size: 0.9em;\n  margin-right: 15px;\n}\n\n.stage-steps {\n  margin-bottom: 10px;\n}\n\n.step-tag {\n  display: inline-block;\n  background: #ecf0f1;\n  color: #2c3e50;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 0.8em;\n  margin-right: 8px;\n  margin-bottom: 4px;\n}\n\n/* JSON预览样式 */\n.json-preview {\n  margin-top: 40px;\n}\n\n.json-tabs {\n  display: flex;\n  margin-bottom: 20px;\n}\n\n.tab-btn {\n  background: #ecf0f1;\n  border: none;\n  padding: 10px 20px;\n  cursor: pointer;\n  border-radius: 4px 4px 0 0;\n  margin-right: 2px;\n  transition: background 0.3s;\n}\n\n.tab-btn.active {\n  background: #3498db;\n  color: white;\n}\n\n.tab-btn:hover:not(.active) {\n  background: #bdc3c7;\n}\n\n.json-content {\n  background: #2c3e50;\n  color: #ecf0f1;\n  padding: 20px;\n  border-radius: 0 4px 4px 4px;\n  overflow-x: auto;\n}\n\n.json-content pre {\n  margin: 0;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .form-row {\n    grid-template-columns: 1fr;\n  }\n  \n  .step-item {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .step-name,\n  .step-duration {\n    flex: none;\n  }\n  \n  .scenario-id,\n  .scenario-owner {\n    display: block;\n    margin-left: 0;\n    margin-top: 5px;\n  }\n}\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EAIrBA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAW;;EAEbA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;EAgBxBA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAW;;EAEbA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAY;;;EAepBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAY;;EAUlBA,KAAK,EAAC;AAAY;;EAYpBA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAY;;;;;EAiCpBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAkB;;EAShCA,KAAK,EAAC;AAAc;;EAIlBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAe;;EAMjBA,KAAK,EAAC;AAAe;;EAElBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAgB;;EACzBA,KAAK,EAAC;AAAe;;EAErBA,KAAK,EAAC;AAAe;;EAClBA,KAAK,EAAC;AAAc;;EAO7BA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAY;;EAMdA,KAAK,EAAC;AAAc;;EAEjBA,KAAK,EAAC;AAAgB;;EACtBA,KAAK,EAAC;AAAgB;;EAEzBA,KAAK,EAAC;AAAa;;EASnBA,KAAK,EAAC;AAAY;;EAS1BA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAW;;EAejBA,KAAK,EAAC;AAAc;;;;;;;;uBAvO7BC,mBAAA,CA4OM,OA5ONC,UA4OM,G,4BA3OJC,mBAAA,CAAgB,YAAZ,SAAO,qBAEXC,mBAAA,YAAe,EACfD,mBAAA,CAkDM,OAlDNE,UAkDM,G,4BAjDJF,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CA+CM,OA/CNG,UA+CM,GA9CJH,mBAAA,CA6CO;IA7CAI,QAAM,EAAAC,MAAA,QAAAA,MAAA,MAAAC,cAAA,KAAAC,IAAA,KAAUC,QAAA,CAAAC,cAAA,IAAAD,QAAA,CAAAC,cAAA,IAAAF,IAAA,CAAc;MACnCP,mBAAA,CASM,OATNU,UASM,G,4BARJV,mBAAA,CAAsC;IAA/BW,GAAG,EAAC;EAAY,GAAC,QAAM,qB,gBAC9BX,mBAAA,CAMC;IALCY,IAAI,EAAC,MAAM;IACXC,EAAE,EAAC,YAAY;+DACNC,KAAA,CAAAC,YAAY,CAACC,WAAW,GAAAC,MAAA;IACjCC,WAAW,EAAC,cAAc;IAC1BC,QAAQ,EAAR;iDAFSL,KAAA,CAAAC,YAAY,CAACC,WAAW,E,KAMrChB,mBAAA,CASM,OATNoB,UASM,G,4BARJpB,mBAAA,CAAsC;IAA/BW,GAAG,EAAC;EAAc,GAAC,MAAI,qB,gBAC9BX,mBAAA,CAMC;IALCY,IAAI,EAAC,MAAM;IACXC,EAAE,EAAC,cAAc;+DACRC,KAAA,CAAAC,YAAY,CAACM,aAAa,GAAAJ,MAAA;IACnCC,WAAW,EAAC,WAAW;IACvBC,QAAQ,EAAR;iDAFSL,KAAA,CAAAC,YAAY,CAACM,aAAa,E,KAMvCrB,mBAAA,CASM,OATNsB,UASM,G,4BARJtB,mBAAA,CAAwC;IAAjCW,GAAG,EAAC;EAAe,GAAC,OAAK,qB,gBAChCX,mBAAA,CAMC;IALCY,IAAI,EAAC,MAAM;IACXC,EAAE,EAAC,eAAe;+DACTC,KAAA,CAAAC,YAAY,CAACQ,cAAc,GAAAN,MAAA;IACpCC,WAAW,EAAC,QAAQ;IACpBC,QAAQ,EAAR;iDAFSL,KAAA,CAAAC,YAAY,CAACQ,cAAc,E,KAMxCvB,mBAAA,CAQM,OARNwB,UAQM,G,4BAPJxB,mBAAA,CAAsC;IAA/BW,GAAG,EAAC;EAAc,GAAC,MAAI,qB,gBAC9BX,mBAAA,CAKY;IAJVa,EAAE,EAAC,cAAc;+DACRC,KAAA,CAAAC,YAAY,CAACU,aAAa,GAAAR,MAAA;IACnCC,WAAW,EAAC,gBAAgB;IAC5BQ,IAAI,EAAC;iDAFIZ,KAAA,CAAAC,YAAY,CAACU,aAAa,E,iCAMvCzB,mBAAA,CAAuD;IAA/CY,IAAI,EAAC,QAAQ;IAACf,KAAK,EAAC;KAAc,MAAI,oB,gCAKpDI,mBAAA,YAAe,EACfD,mBAAA,CAiGM,OAjGN2B,UAiGM,G,4BAhGJ3B,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CA8FM,OA9FN4B,UA8FM,GA7FJ5B,mBAAA,CA4FO;IA5FAI,QAAM,EAAAC,MAAA,SAAAA,MAAA,OAAAC,cAAA,KAAAC,IAAA,KAAUC,QAAA,CAAAqB,eAAA,IAAArB,QAAA,CAAAqB,eAAA,IAAAtB,IAAA,CAAe;MACpCP,mBAAA,CAyBM,OAzBN8B,WAyBM,GAxBJ9B,mBAAA,CASM,OATN+B,WASM,G,4BARJ/B,mBAAA,CAAuC;IAAhCW,GAAG,EAAC;EAAa,GAAC,QAAM,qB,gBAC/BX,mBAAA,CAMC;IALCY,IAAI,EAAC,MAAM;IACXC,EAAE,EAAC,aAAa;+DACPC,KAAA,CAAAkB,SAAS,CAACC,YAAY,GAAAhB,MAAA;IAC/BC,WAAW,EAAC,wBAAwB;IACpCC,QAAQ,EAAR;iDAFSL,KAAA,CAAAkB,SAAS,CAACC,YAAY,E,KAMnCjC,mBAAA,CAYM,OAZNkC,WAYM,G,4BAXJlC,mBAAA,CAAyC;IAAlCW,GAAG,EAAC;EAAiB,GAAC,MAAI,qB,gBACjCX,mBAAA,CASS;IATDa,EAAE,EAAC,iBAAiB;+DAAUC,KAAA,CAAAkB,SAAS,CAAChB,WAAW,GAAAC,MAAA;IAAEE,QAAQ,EAAR;kCAC3DnB,mBAAA,CAA+B;IAAvBmC,KAAK,EAAC;EAAE,GAAC,OAAK,sB,kBACtBrC,mBAAA,CAMSsC,SAAA,QAAAC,WAAA,CALYvB,KAAA,CAAAwB,SAAS,EAArBC,QAAQ;yBADjBzC,mBAAA,CAMS;MAJN0C,GAAG,EAAED,QAAQ,CAACvB,WAAW;MACzBmB,KAAK,EAAEI,QAAQ,CAACvB;wBAEduB,QAAQ,CAAClB,aAAa,wBAAAoB,WAAA;2EAPS3B,KAAA,CAAAkB,SAAS,CAAChB,WAAW,E,OAa/DhB,mBAAA,CAoBM,OApBN0C,WAoBM,GAnBJ1C,mBAAA,CAQM,OARN2C,WAQM,G,4BAPJ3C,mBAAA,CAAmC;IAA5BW,GAAG,EAAC;EAAW,GAAC,MAAI,qB,gBAC3BX,mBAAA,CAKC;IAJCY,IAAI,EAAC,gBAAgB;IACrBC,EAAE,EAAC,WAAW;+DACLC,KAAA,CAAAkB,SAAS,CAACY,UAAU,GAAA3B,MAAA;IAC7BE,QAAQ,EAAR;iDADSL,KAAA,CAAAkB,SAAS,CAACY,UAAU,E,KAKjC5C,mBAAA,CAQM,OARN6C,WAQM,G,4BAPJ7C,mBAAA,CAAiC;IAA1BW,GAAG,EAAC;EAAS,GAAC,MAAI,qB,gBACzBX,mBAAA,CAKC;IAJCY,IAAI,EAAC,gBAAgB;IACrBC,EAAE,EAAC,SAAS;+DACHC,KAAA,CAAAkB,SAAS,CAACc,QAAQ,GAAA7B,MAAA;IAC3BE,QAAQ,EAAR;iDADSL,KAAA,CAAAkB,SAAS,CAACc,QAAQ,E,OAMjC7C,mBAAA,YAAe,EACfD,mBAAA,CAiCM,OAjCN+C,WAiCM,G,4BAhCJ/C,mBAAA,CAAyB,eAAlB,YAAU,qBACjBA,mBAAA,CA6BM,OA7BNgD,WA6BM,I,kBA5BJlD,mBAAA,CA2BMsC,SAAA,QAAAC,WAAA,CA1BoBvB,KAAA,CAAAkB,SAAS,CAACiB,cAAc,GAAxCC,IAAI,EAAEC,KAAK;yBADrBrD,mBAAA,CA2BM;MAzBH0C,GAAG,EAAEW,KAAK;MACXtD,KAAK,EAAC;wBAENG,mBAAA,CAKC;MAJCY,IAAI,EAAC,MAAM;uCACFsC,IAAI,CAACE,IAAI,GAAAnC,MAAA;MAClBC,WAAW,EAAC,MAAM;MAClBrB,KAAK,EAAC;yDAFGqD,IAAI,CAACE,IAAI,E,mBAIpBpD,mBAAA,CAOC;MANCY,IAAI,EAAC,QAAQ;uCACGsC,IAAI,CAACG,QAAQ,GAAApC,MAAA;MAC7BC,WAAW,EAAC,IAAI;MAChBgC,IAAI,EAAC,KAAK;MACVI,GAAG,EAAC,GAAG;MACPzD,KAAK,EAAC;yDAJUqD,IAAI,CAACG,QAAQ,E;MAArBE,MAAM,EAAd;IAA8B,E,IAUxBzC,KAAA,CAAAkB,SAAS,CAACiB,cAAc,CAACO,MAAM,Q,cAJvC1D,mBAAA,CAOS;;MANPc,IAAI,EAAC,QAAQ;MACZ6C,OAAK,EAAAxC,MAAA,IAAET,QAAA,CAAAkD,UAAU,CAACP,KAAK;MACxBtD,KAAK,EAAC;OAEP,KAED,iBAAA8D,WAAA,K;oCAGJ3D,mBAAA,CAA0E;IAAlEY,IAAI,EAAC,QAAQ;IAAE6C,OAAK,EAAApD,MAAA,QAAAA,MAAA,UAAAE,IAAA,KAAEC,QAAA,CAAAoD,OAAA,IAAApD,QAAA,CAAAoD,OAAA,IAAArD,IAAA,CAAO;IAAEV,KAAK,EAAC;KAAgB,MAAI,E,GAGnEG,mBAAA,CAGM,OAHN6D,WAGM,G,4BAFJ7D,mBAAA,CAAwB,eAAjB,WAAS,qBAChBA,mBAAA,CAAqE,OAArE8D,WAAqE,EAAAC,gBAAA,CAApCvD,QAAA,CAAAwD,aAAa,CAACC,OAAO,OAAM,KAAG,gB,+BAGjEjE,mBAAA,CAAyD;IAAjDY,IAAI,EAAC,QAAQ;IAACf,KAAK,EAAC;KAAc,QAAM,oB,gCAKtDI,mBAAA,UAAa,EACbD,mBAAA,CAsDM,OAtDNkE,WAsDM,G,4BArDJlE,mBAAA,CAAa,YAAT,MAAI,qBAERC,mBAAA,UAAa,EACbD,mBAAA,CAmBM,OAnBNmE,WAmBM,G,4BAlBJnE,mBAAA,CAAc,YAAV,OAAK,qBACTA,mBAAA,CAgBM,OAhBNoE,WAgBM,I,kBAfJtE,mBAAA,CAcMsC,SAAA,QAAAC,WAAA,CAbevB,KAAA,CAAAwB,SAAS,EAArBC,QAAQ;yBADjBzC,mBAAA,CAcM;MAZH0C,GAAG,EAAED,QAAQ,CAACvB,WAAW;MAC1BnB,KAAK,EAAC;QAENG,mBAAA,CAKM,OALNqE,WAKM,GAJJrE,mBAAA,CAA6C,gBAAA+D,gBAAA,CAAlCxB,QAAQ,CAAClB,aAAa,kBACjCrB,mBAAA,CAA+D,QAA/DsE,WAA+D,EAArC,MAAI,GAAAP,gBAAA,CAAGxB,QAAQ,CAACvB,WAAW,kBACrDhB,mBAAA,CAAsE,QAAtEuE,WAAsE,EAAzC,OAAK,GAAAR,gBAAA,CAAGxB,QAAQ,CAAChB,cAAc,kBAC5DvB,mBAAA,CAAyD,KAAzDwE,WAAyD,EAAAT,gBAAA,CAA7BxB,QAAQ,CAACd,aAAa,iB,GAEpDzB,mBAAA,CAEM,OAFNyE,WAEM,GADJzE,mBAAA,CAAuE,QAAvE0E,WAAuE,EAAAX,gBAAA,CAAzCvD,QAAA,CAAAmE,UAAU,CAACpC,QAAQ,CAACqC,UAAU,kB;sCAMpE3E,mBAAA,YAAe,EACfD,mBAAA,CA2BM,OA3BN6E,WA2BM,G,4BA1BJ7E,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAwBM,OAxBN8E,WAwBM,I,kBAvBJhF,mBAAA,CAsBMsC,SAAA,QAAAC,WAAA,CArBYvB,KAAA,CAAAiE,YAAY,EAArBC,KAAK;yBADdlF,mBAAA,CAsBM;MApBH0C,GAAG,EAAEwC,KAAK,CAAC/C,YAAY;MACxBpC,KAAK,EAAC;QAENG,mBAAA,CAIM,OAJNiF,WAIM,GAHJjF,mBAAA,CAAyC,gBAAA+D,gBAAA,CAA9BiB,KAAK,CAAC/C,YAAY,kBAC7BjC,mBAAA,CAAgF,QAAhFkF,WAAgF,EAAnD,MAAI,GAAAnB,gBAAA,CAAGvD,QAAA,CAAA2E,eAAe,CAACH,KAAK,CAAChE,WAAW,mBACrEhB,mBAAA,CAA6E,QAA7EoF,WAA6E,EAAhD,OAAK,GAAArB,gBAAA,CAAGiB,KAAK,CAACK,sBAAsB,IAAG,IAAE,gB,GAExErF,mBAAA,CAQM,OARNsF,WAQM,I,kBAPJxF,mBAAA,CAMOsC,SAAA,QAAAC,WAAA,CAL0B2C,KAAK,CAAC/B,cAAc,GAA3CI,QAAQ,EAAEkC,QAAQ;2BAD5BzF,mBAAA,CAMO;QAJJ0C,GAAG,EAAE+C,QAAQ;QACd1F,KAAK,EAAC;0BAEH0F,QAAQ,IAAG,IAAE,GAAAxB,gBAAA,CAAGV,QAAQ,IAAG,KAChC;sCAEFrD,mBAAA,CAEM,OAFNwF,WAEM,EAAAzB,gBAAA,CADDvD,QAAA,CAAAmE,UAAU,CAACK,KAAK,CAACpC,UAAU,KAAI,KAAG,GAAAmB,gBAAA,CAAGvD,QAAA,CAAAmE,UAAU,CAACK,KAAK,CAAClC,QAAQ,kB;wCAO3E7C,mBAAA,cAAiB,EACjBD,mBAAA,CAqBM,OArBNyF,WAqBM,G,4BApBJzF,mBAAA,CAAiB,YAAb,UAAQ,qBACZA,mBAAA,CAaM,OAbN0F,WAaM,GAZJ1F,mBAAA,CAKS;IAJNH,KAAK,EAAA8F,eAAA;MAAAC,MAAA,EAAwB9E,KAAA,CAAA+E,SAAS;IAAA;IACtCpC,OAAK,EAAApD,MAAA,SAAAA,MAAA,OAAAY,MAAA,IAAEH,KAAA,CAAA+E,SAAS;KAClB,QAED,kBACA7F,mBAAA,CAKS;IAJNH,KAAK,EAAA8F,eAAA;MAAAC,MAAA,EAAwB9E,KAAA,CAAA+E,SAAS;IAAA;IACtCpC,OAAK,EAAApD,MAAA,SAAAA,MAAA,OAAAY,MAAA,IAAEH,KAAA,CAAA+E,SAAS;KAClB,QAED,iB,GAGF7F,mBAAA,CAGM,OAHN8F,WAGM,GAFOhF,KAAA,CAAA+E,SAAS,mB,cAApB/F,mBAAA,CAAoE,OAAAiG,WAAA,EAAAhC,gBAAA,CAA5BvD,QAAA,CAAAwF,mBAAmB,oB,mCAChDlF,KAAA,CAAA+E,SAAS,gB,cAApB/F,mBAAA,CAA8D,OAAAmG,WAAA,EAAAlC,gBAAA,CAAzBvD,QAAA,CAAA0F,gBAAgB,oB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}