{"ast": null, "code": "import HelloWorld from './components/HelloWorld.vue';\nexport default {\n  name: 'App',\n  components: {\n    HelloWorld\n  }\n};", "map": {"version": 3, "names": ["HelloWorld", "name", "components"], "sources": ["/Users/<USER>/Desktop/dev/frontend-project/src/App.vue"], "sourcesContent": ["<template>\n  <img alt=\"Vue logo\" src=\"./assets/logo.png\">\n  <HelloWorld msg=\"Welcome to Your Vue.js App\"/>\n</template>\n\n<script>\nimport HelloWorld from './components/HelloWorld.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    HelloWorld\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-align: center;\n  color: #2c3e50;\n  margin-top: 60px;\n}\n</style>\n"], "mappings": "AAMA,OAAOA,UAAS,MAAO,6BAA4B;AAEnD,eAAe;EACbC,IAAI,EAAE,KAAK;EACXC,UAAU,EAAE;IACVF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}