{"ast": null, "code": "import { createRouter, createWebHistory } from 'vue-router';\nimport Dashboard from '../views/Dashboard.vue';\nimport DataCollection from '../components/DataCollection.vue';\nimport EfficiencyMetrics from '../components/EfficiencyMetrics.vue';\nconst routes = [{\n  path: '/',\n  name: 'Dashboard',\n  component: Dashboard\n}, {\n  path: '/data-collection',\n  name: 'DataCollection',\n  component: DataCollection\n}, {\n  path: '/metrics',\n  name: 'EfficiencyMetrics',\n  component: EfficiencyMetrics\n}];\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n});\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "Dashboard", "DataCollection", "EfficiencyMetrics", "routes", "path", "name", "component", "router", "history", "process", "env", "BASE_URL"], "sources": ["/Users/<USER>/Desktop/dev/frontend-project/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\nimport Dashboard from '../views/Dashboard.vue'\nimport DataCollection from '../components/DataCollection.vue'\nimport EfficiencyMetrics from '../components/EfficiencyMetrics.vue'\n\nconst routes = [\n  {\n    path: '/',\n    name: 'Dashboard',\n    component: Dashboard\n  },\n  {\n    path: '/data-collection',\n    name: 'DataCollection',\n    component: DataCollection\n  },\n  {\n    path: '/metrics',\n    name: 'EfficiencyMetrics',\n    component: EfficiencyMetrics\n  }\n]\n\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n})\n\nexport default router"], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,iBAAiB,MAAM,qCAAqC;AAEnE,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEN;AACb,CAAC,EACD;EACEI,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEL;AACb,CAAC,EACD;EACEG,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEJ;AACb,CAAC,CACF;AAED,MAAMK,MAAM,GAAGT,YAAY,CAAC;EAC1BU,OAAO,EAAET,gBAAgB,CAACU,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;EAC/CR;AACF,CAAC,CAAC;AAEF,eAAeI,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}