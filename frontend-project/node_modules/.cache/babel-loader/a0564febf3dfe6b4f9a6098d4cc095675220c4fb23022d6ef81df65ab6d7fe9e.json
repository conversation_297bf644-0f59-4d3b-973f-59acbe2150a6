{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nexport default {\n  name: 'DataCollection',\n  data() {\n    return {\n      activeTab: 'scenario',\n      // 场景创建表单\n      scenarioForm: {\n        scenario_id: '',\n        scenario_name: '',\n        scenario_owner: '',\n        scenario_desc: ''\n      },\n      // 阶段耗时表单\n      stageForm: {\n        execution_id: '',\n        scenario_id: '',\n        started_at: '',\n        ended_at: '',\n        step_durations: [{\n          name: '依赖安装',\n          duration: 0\n        }, {\n          name: '编译',\n          duration: 0\n        }, {\n          name: '打包',\n          duration: 0\n        }]\n      },\n      // 数据存储\n      scenarios: [{\n        scenario_id: 'proj_abc',\n        scenario_name: '组件库优化',\n        scenario_owner: '李四',\n        created_at: '2024-06-01T09:00:00Z',\n        scenario_desc: '组件库性能与复用优化'\n      }],\n      stageRecords: [{\n        execution_id: 'build_20240601_001',\n        scenario_id: 'proj_abc',\n        started_at: '2024-06-01T10:00:00Z',\n        ended_at: '2024-06-01T10:05:00Z',\n        step_durations: {\n          '依赖安装': 1.5,\n          '编译': 2.0,\n          '打包': 1.5\n        },\n        total_duration_minutes: 5.0\n      }]\n    };\n  },\n  computed: {\n    totalDuration() {\n      return this.stageForm.step_durations.reduce((total, step) => {\n        return total + (step.duration || 0);\n      }, 0);\n    },\n    scenarioJsonExample() {\n      return JSON.stringify({\n        scenario_id: 'proj_abc',\n        scenario_name: '组件库优化',\n        scenario_owner: '李四',\n        created_at: '2024-06-01T09:00:00Z',\n        scenario_desc: '组件库性能与复用优化'\n      }, null, 2);\n    },\n    stageJsonExample() {\n      return JSON.stringify({\n        execution_id: 'build_20240601_001',\n        scenario_id: 'proj_abc',\n        started_at: '2024-06-01T10:00:00Z',\n        ended_at: '2024-06-01T10:05:00Z',\n        step_durations: {\n          '依赖安装': 1.5,\n          '编译': 2.0,\n          '打包': 1.5\n        },\n        total_duration_minutes: 5.0\n      }, null, 2);\n    }\n  },\n  methods: {\n    createScenario() {\n      const newScenario = {\n        ...this.scenarioForm,\n        created_at: new Date().toISOString()\n      };\n      this.scenarios.push(newScenario);\n\n      // 重置表单\n      this.scenarioForm = {\n        scenario_id: '',\n        scenario_name: '',\n        scenario_owner: '',\n        scenario_desc: ''\n      };\n      alert('场景创建成功！');\n    },\n    submitStageData() {\n      // 转换步骤数据格式\n      const stepDurations = {};\n      this.stageForm.step_durations.forEach(step => {\n        if (step.name && step.duration) {\n          stepDurations[step.name] = step.duration;\n        }\n      });\n      const newStageRecord = {\n        execution_id: this.stageForm.execution_id,\n        scenario_id: this.stageForm.scenario_id,\n        started_at: this.stageForm.started_at,\n        ended_at: this.stageForm.ended_at,\n        step_durations: stepDurations,\n        total_duration_minutes: this.totalDuration\n      };\n      this.stageRecords.push(newStageRecord);\n\n      // 重置表单\n      this.stageForm = {\n        execution_id: '',\n        scenario_id: '',\n        started_at: '',\n        ended_at: '',\n        step_durations: [{\n          name: '依赖安装',\n          duration: 0\n        }, {\n          name: '编译',\n          duration: 0\n        }, {\n          name: '打包',\n          duration: 0\n        }]\n      };\n      alert('阶段数据提交成功！');\n    },\n    addStep() {\n      this.stageForm.step_durations.push({\n        name: '',\n        duration: 0\n      });\n    },\n    removeStep(index) {\n      this.stageForm.step_durations.splice(index, 1);\n    },\n    getScenarioName(scenarioId) {\n      const scenario = this.scenarios.find(s => s.scenario_id === scenarioId);\n      return scenario ? scenario.scenario_name : scenarioId;\n    },\n    formatTime(timeString) {\n      return new Date(timeString).toLocaleString('zh-CN');\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "activeTab", "scenarioForm", "scenario_id", "scenario_name", "scenario_owner", "scenario_desc", "stageForm", "execution_id", "started_at", "ended_at", "step_durations", "duration", "scenarios", "created_at", "stageRecords", "total_duration_minutes", "computed", "totalDuration", "reduce", "total", "step", "scenario<PERSON><PERSON>Example", "JSON", "stringify", "stage<PERSON>sonExample", "methods", "createScenario", "newScenario", "Date", "toISOString", "push", "alert", "submitStageData", "stepDurations", "for<PERSON>ach", "newStageRecord", "addStep", "removeStep", "index", "splice", "getScenarioName", "scenarioId", "scenario", "find", "s", "formatTime", "timeString", "toLocaleString"], "sources": ["/Users/<USER>/Desktop/dev/frontend-project/src/components/DataCollection.vue"], "sourcesContent": ["<template>\n  <div class=\"data-collection\">\n    <h2>数据采集与上报</h2>\n    \n    <!-- 场景创建表单 -->\n    <div class=\"scenario-creation\">\n      <h3>场景创建</h3>\n      <div class=\"form-card\">\n        <form @submit.prevent=\"createScenario\">\n          <div class=\"form-group\">\n            <label for=\"scenarioId\">场景唯一标识</label>\n            <input \n              type=\"text\" \n              id=\"scenarioId\" \n              v-model=\"scenarioForm.scenario_id\" \n              placeholder=\"例如: proj_abc\"\n              required\n            >\n          </div>\n          \n          <div class=\"form-group\">\n            <label for=\"scenarioName\">场景名称</label>\n            <input \n              type=\"text\" \n              id=\"scenarioName\" \n              v-model=\"scenarioForm.scenario_name\" \n              placeholder=\"例如: 组件库优化\"\n              required\n            >\n          </div>\n          \n          <div class=\"form-group\">\n            <label for=\"scenarioOwner\">场景负责人</label>\n            <input \n              type=\"text\" \n              id=\"scenarioOwner\" \n              v-model=\"scenarioForm.scenario_owner\" \n              placeholder=\"例如: 李四\"\n              required\n            >\n          </div>\n          \n          <div class=\"form-group\">\n            <label for=\"scenarioDesc\">场景描述</label>\n            <textarea \n              id=\"scenarioDesc\" \n              v-model=\"scenarioForm.scenario_desc\" \n              placeholder=\"例如: 组件库性能与复用优化\"\n              rows=\"3\"\n            ></textarea>\n          </div>\n          \n          <button type=\"submit\" class=\"btn-primary\">创建场景</button>\n        </form>\n      </div>\n    </div>\n    \n    <!-- 阶段耗时采集 -->\n    <div class=\"stage-collection\">\n      <h3>阶段耗时采集</h3>\n      <div class=\"form-card\">\n        <form @submit.prevent=\"submitStageData\">\n          <div class=\"form-row\">\n            <div class=\"form-group\">\n              <label for=\"executionId\">执行记录ID</label>\n              <input \n                type=\"text\" \n                id=\"executionId\" \n                v-model=\"stageForm.execution_id\" \n                placeholder=\"例如: build_20240601_001\"\n                required\n              >\n            </div>\n            \n            <div class=\"form-group\">\n              <label for=\"relatedScenario\">关联场景</label>\n              <select id=\"relatedScenario\" v-model=\"stageForm.scenario_id\" required>\n                <option value=\"\">请选择场景</option>\n                <option \n                  v-for=\"scenario in scenarios\" \n                  :key=\"scenario.scenario_id\" \n                  :value=\"scenario.scenario_id\"\n                >\n                  {{ scenario.scenario_name }}\n                </option>\n              </select>\n            </div>\n          </div>\n          \n          <div class=\"form-row\">\n            <div class=\"form-group\">\n              <label for=\"startTime\">开始时间</label>\n              <input \n                type=\"datetime-local\" \n                id=\"startTime\" \n                v-model=\"stageForm.started_at\" \n                required\n              >\n            </div>\n            \n            <div class=\"form-group\">\n              <label for=\"endTime\">结束时间</label>\n              <input \n                type=\"datetime-local\" \n                id=\"endTime\" \n                v-model=\"stageForm.ended_at\" \n                required\n              >\n            </div>\n          </div>\n          \n          <!-- 步骤耗时明细 -->\n          <div class=\"steps-section\">\n            <label>步骤耗时明细（分钟）</label>\n            <div class=\"steps-grid\">\n              <div \n                v-for=\"(step, index) in stageForm.step_durations\" \n                :key=\"index\" \n                class=\"step-item\"\n              >\n                <input \n                  type=\"text\" \n                  v-model=\"step.name\" \n                  placeholder=\"步骤名称\"\n                  class=\"step-name\"\n                >\n                <input \n                  type=\"number\" \n                  v-model.number=\"step.duration\" \n                  placeholder=\"耗时\"\n                  step=\"0.1\"\n                  min=\"0\"\n                  class=\"step-duration\"\n                >\n                <button \n                  type=\"button\" \n                  @click=\"removeStep(index)\" \n                  class=\"btn-remove\"\n                  v-if=\"stageForm.step_durations.length > 1\"\n                >\n                  ×\n                </button>\n              </div>\n            </div>\n            <button type=\"button\" @click=\"addStep\" class=\"btn-secondary\">添加步骤</button>\n          </div>\n          \n          <div class=\"form-group\">\n            <label>总耗时（自动计算）</label>\n            <div class=\"calculated-value\">{{ totalDuration.toFixed(1) }} 分钟</div>\n          </div>\n          \n          <button type=\"submit\" class=\"btn-primary\">提交阶段数据</button>\n        </form>\n      </div>\n    </div>\n    \n    <!-- 数据预览 -->\n    <div class=\"data-preview\">\n      <h3>数据预览</h3>\n      \n      <!-- 场景列表 -->\n      <div class=\"preview-section\">\n        <h4>已创建场景</h4>\n        <div class=\"scenario-list\">\n          <div \n            v-for=\"scenario in scenarios\" \n            :key=\"scenario.scenario_id\" \n            class=\"scenario-item\"\n          >\n            <div class=\"scenario-info\">\n              <strong>{{ scenario.scenario_name }}</strong>\n              <span class=\"scenario-id\">ID: {{ scenario.scenario_id }}</span>\n              <span class=\"scenario-owner\">负责人: {{ scenario.scenario_owner }}</span>\n              <p class=\"scenario-desc\">{{ scenario.scenario_desc }}</p>\n            </div>\n            <div class=\"scenario-meta\">\n              <span class=\"created-time\">{{ formatTime(scenario.created_at) }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 阶段数据列表 -->\n      <div class=\"preview-section\">\n        <h4>阶段耗时记录</h4>\n        <div class=\"stage-list\">\n          <div \n            v-for=\"stage in stageRecords\" \n            :key=\"stage.execution_id\" \n            class=\"stage-item\"\n          >\n            <div class=\"stage-header\">\n              <strong>{{ stage.execution_id }}</strong>\n              <span class=\"stage-scenario\">场景: {{ getScenarioName(stage.scenario_id) }}</span>\n              <span class=\"stage-duration\">总耗时: {{ stage.total_duration_minutes }}分钟</span>\n            </div>\n            <div class=\"stage-steps\">\n              <span \n                v-for=\"(duration, stepName) in stage.step_durations\" \n                :key=\"stepName\" \n                class=\"step-tag\"\n              >\n                {{ stepName }}: {{ duration }}分钟\n              </span>\n            </div>\n            <div class=\"stage-time\">\n              {{ formatTime(stage.started_at) }} - {{ formatTime(stage.ended_at) }}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- JSON数据展示 -->\n    <div class=\"json-preview\">\n      <h3>JSON数据格式</h3>\n      <div class=\"json-tabs\">\n        <button \n          :class=\"['tab-btn', { active: activeTab === 'scenario' }]\" \n          @click=\"activeTab = 'scenario'\"\n        >\n          场景创建\n        </button>\n        <button \n          :class=\"['tab-btn', { active: activeTab === 'stage' }]\" \n          @click=\"activeTab = 'stage'\"\n        >\n          阶段耗时\n        </button>\n      </div>\n      \n      <div class=\"json-content\">\n        <pre v-if=\"activeTab === 'scenario'\">{{ scenarioJsonExample }}</pre>\n        <pre v-if=\"activeTab === 'stage'\">{{ stageJsonExample }}</pre>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'DataCollection',\n  data() {\n    return {\n      activeTab: 'scenario',\n      \n      // 场景创建表单\n      scenarioForm: {\n        scenario_id: '',\n        scenario_name: '',\n        scenario_owner: '',\n        scenario_desc: ''\n      },\n      \n      // 阶段耗时表单\n      stageForm: {\n        execution_id: '',\n        scenario_id: '',\n        started_at: '',\n        ended_at: '',\n        step_durations: [\n          { name: '依赖安装', duration: 0 },\n          { name: '编译', duration: 0 },\n          { name: '打包', duration: 0 }\n        ]\n      },\n      \n      // 数据存储\n      scenarios: [\n        {\n          scenario_id: 'proj_abc',\n          scenario_name: '组件库优化',\n          scenario_owner: '李四',\n          created_at: '2024-06-01T09:00:00Z',\n          scenario_desc: '组件库性能与复用优化'\n        }\n      ],\n      \n      stageRecords: [\n        {\n          execution_id: 'build_20240601_001',\n          scenario_id: 'proj_abc',\n          started_at: '2024-06-01T10:00:00Z',\n          ended_at: '2024-06-01T10:05:00Z',\n          step_durations: {\n            '依赖安装': 1.5,\n            '编译': 2.0,\n            '打包': 1.5\n          },\n          total_duration_minutes: 5.0\n        }\n      ]\n    }\n  },\n  \n  computed: {\n    totalDuration() {\n      return this.stageForm.step_durations.reduce((total, step) => {\n        return total + (step.duration || 0)\n      }, 0)\n    },\n    \n    scenarioJsonExample() {\n      return JSON.stringify({\n        scenario_id: 'proj_abc',\n        scenario_name: '组件库优化',\n        scenario_owner: '李四',\n        created_at: '2024-06-01T09:00:00Z',\n        scenario_desc: '组件库性能与复用优化'\n      }, null, 2)\n    },\n    \n    stageJsonExample() {\n      return JSON.stringify({\n        execution_id: 'build_20240601_001',\n        scenario_id: 'proj_abc',\n        started_at: '2024-06-01T10:00:00Z',\n        ended_at: '2024-06-01T10:05:00Z',\n        step_durations: {\n          '依赖安装': 1.5,\n          '编译': 2.0,\n          '打包': 1.5\n        },\n        total_duration_minutes: 5.0\n      }, null, 2)\n    }\n  },\n  \n  methods: {\n    createScenario() {\n      const newScenario = {\n        ...this.scenarioForm,\n        created_at: new Date().toISOString()\n      }\n      \n      this.scenarios.push(newScenario)\n      \n      // 重置表单\n      this.scenarioForm = {\n        scenario_id: '',\n        scenario_name: '',\n        scenario_owner: '',\n        scenario_desc: ''\n      }\n      \n      alert('场景创建成功！')\n    },\n    \n    submitStageData() {\n      // 转换步骤数据格式\n      const stepDurations = {}\n      this.stageForm.step_durations.forEach(step => {\n        if (step.name && step.duration) {\n          stepDurations[step.name] = step.duration\n        }\n      })\n      \n      const newStageRecord = {\n        execution_id: this.stageForm.execution_id,\n        scenario_id: this.stageForm.scenario_id,\n        started_at: this.stageForm.started_at,\n        ended_at: this.stageForm.ended_at,\n        step_durations: stepDurations,\n        total_duration_minutes: this.totalDuration\n      }\n      \n      this.stageRecords.push(newStageRecord)\n      \n      // 重置表单\n      this.stageForm = {\n        execution_id: '',\n        scenario_id: '',\n        started_at: '',\n        ended_at: '',\n        step_durations: [\n          { name: '依赖安装', duration: 0 },\n          { name: '编译', duration: 0 },\n          { name: '打包', duration: 0 }\n        ]\n      }\n      \n      alert('阶段数据提交成功！')\n    },\n    \n    addStep() {\n      this.stageForm.step_durations.push({ name: '', duration: 0 })\n    },\n    \n    removeStep(index) {\n      this.stageForm.step_durations.splice(index, 1)\n    },\n    \n    getScenarioName(scenarioId) {\n      const scenario = this.scenarios.find(s => s.scenario_id === scenarioId)\n      return scenario ? scenario.scenario_name : scenarioId\n    },\n    \n    formatTime(timeString) {\n      return new Date(timeString).toLocaleString('zh-CN')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.data-collection {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.data-collection h2 {\n  color: #2c3e50;\n  margin-bottom: 30px;\n  border-bottom: 2px solid #3498db;\n  padding-bottom: 10px;\n}\n\n.data-collection h3 {\n  color: #34495e;\n  margin-bottom: 20px;\n}\n\n.data-collection h4 {\n  color: #2c3e50;\n  margin-bottom: 15px;\n  font-size: 1.1em;\n}\n\n/* 表单样式 */\n.form-card {\n  background: white;\n  border-radius: 8px;\n  padding: 24px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  margin-bottom: 30px;\n}\n\n.form-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n}\n\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 8px;\n  font-weight: 500;\n  color: #2c3e50;\n}\n\n.form-group input,\n.form-group select,\n.form-group textarea {\n  width: 100%;\n  padding: 10px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n  transition: border-color 0.3s;\n}\n\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #3498db;\n}\n\n/* 步骤耗时样式 */\n.steps-section {\n  margin-bottom: 20px;\n}\n\n.steps-section label {\n  display: block;\n  margin-bottom: 12px;\n  font-weight: 500;\n  color: #2c3e50;\n}\n\n.steps-grid {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n  margin-bottom: 15px;\n}\n\n.step-item {\n  display: flex;\n  gap: 10px;\n  align-items: center;\n}\n\n.step-name {\n  flex: 2;\n}\n\n.step-duration {\n  flex: 1;\n}\n\n.btn-remove {\n  background: #e74c3c;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  width: 30px;\n  height: 30px;\n  cursor: pointer;\n  font-size: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-remove:hover {\n  background: #c0392b;\n}\n\n.calculated-value {\n  background: #f8f9fa;\n  padding: 10px;\n  border-radius: 4px;\n  font-weight: 500;\n  color: #2c3e50;\n}\n\n/* 按钮样式 */\n.btn-primary {\n  background: #3498db;\n  color: white;\n  border: none;\n  padding: 12px 24px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  font-weight: 500;\n  transition: background 0.3s;\n}\n\n.btn-primary:hover {\n  background: #2980b9;\n}\n\n.btn-secondary {\n  background: #95a5a6;\n  color: white;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: background 0.3s;\n}\n\n.btn-secondary:hover {\n  background: #7f8c8d;\n}\n\n/* 预览区域样式 */\n.data-preview {\n  margin-top: 40px;\n}\n\n.preview-section {\n  margin-bottom: 30px;\n}\n\n.scenario-list,\n.stage-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.scenario-item,\n.stage-item {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  border-left: 4px solid #3498db;\n}\n\n.scenario-info {\n  margin-bottom: 10px;\n}\n\n.scenario-info strong {\n  color: #2c3e50;\n  font-size: 1.1em;\n}\n\n.scenario-id,\n.scenario-owner {\n  display: inline-block;\n  margin-left: 15px;\n  color: #7f8c8d;\n  font-size: 0.9em;\n}\n\n.scenario-desc {\n  margin: 8px 0 0 0;\n  color: #34495e;\n}\n\n.scenario-meta,\n.stage-time {\n  color: #95a5a6;\n  font-size: 0.9em;\n}\n\n.stage-header {\n  margin-bottom: 10px;\n}\n\n.stage-header strong {\n  color: #2c3e50;\n  margin-right: 15px;\n}\n\n.stage-scenario,\n.stage-duration {\n  color: #7f8c8d;\n  font-size: 0.9em;\n  margin-right: 15px;\n}\n\n.stage-steps {\n  margin-bottom: 10px;\n}\n\n.step-tag {\n  display: inline-block;\n  background: #ecf0f1;\n  color: #2c3e50;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 0.8em;\n  margin-right: 8px;\n  margin-bottom: 4px;\n}\n\n/* JSON预览样式 */\n.json-preview {\n  margin-top: 40px;\n}\n\n.json-tabs {\n  display: flex;\n  margin-bottom: 20px;\n}\n\n.tab-btn {\n  background: #ecf0f1;\n  border: none;\n  padding: 10px 20px;\n  cursor: pointer;\n  border-radius: 4px 4px 0 0;\n  margin-right: 2px;\n  transition: background 0.3s;\n}\n\n.tab-btn.active {\n  background: #3498db;\n  color: white;\n}\n\n.tab-btn:hover:not(.active) {\n  background: #bdc3c7;\n}\n\n.json-content {\n  background: #2c3e50;\n  color: #ecf0f1;\n  padding: 20px;\n  border-radius: 0 4px 4px 4px;\n  overflow-x: auto;\n}\n\n.json-content pre {\n  margin: 0;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .form-row {\n    grid-template-columns: 1fr;\n  }\n  \n  .step-item {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .step-name,\n  .step-duration {\n    flex: none;\n  }\n  \n  .scenario-id,\n  .scenario-owner {\n    display: block;\n    margin-left: 0;\n    margin-top: 5px;\n  }\n}\n</style>"], "mappings": ";;;;;AAiPA,eAAe;EACbA,IAAI,EAAE,gBAAgB;EACtBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,UAAU;MAErB;MACAC,YAAY,EAAE;QACZC,WAAW,EAAE,EAAE;QACfC,aAAa,EAAE,EAAE;QACjBC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE;MACjB,CAAC;MAED;MACAC,SAAS,EAAE;QACTC,YAAY,EAAE,EAAE;QAChBL,WAAW,EAAE,EAAE;QACfM,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,EAAE;QACZC,cAAc,EAAE,CACd;UAAEZ,IAAI,EAAE,MAAM;UAAEa,QAAQ,EAAE;QAAE,CAAC,EAC7B;UAAEb,IAAI,EAAE,IAAI;UAAEa,QAAQ,EAAE;QAAE,CAAC,EAC3B;UAAEb,IAAI,EAAE,IAAI;UAAEa,QAAQ,EAAE;QAAE;MAE9B,CAAC;MAED;MACAC,SAAS,EAAE,CACT;QACEV,WAAW,EAAE,UAAU;QACvBC,aAAa,EAAE,OAAO;QACtBC,cAAc,EAAE,IAAI;QACpBS,UAAU,EAAE,sBAAsB;QAClCR,aAAa,EAAE;MACjB,EACD;MAEDS,YAAY,EAAE,CACZ;QACEP,YAAY,EAAE,oBAAoB;QAClCL,WAAW,EAAE,UAAU;QACvBM,UAAU,EAAE,sBAAsB;QAClCC,QAAQ,EAAE,sBAAsB;QAChCC,cAAc,EAAE;UACd,MAAM,EAAE,GAAG;UACX,IAAI,EAAE,GAAG;UACT,IAAI,EAAE;QACR,CAAC;QACDK,sBAAsB,EAAE;MAC1B;IAEJ;EACF,CAAC;EAEDC,QAAQ,EAAE;IACRC,aAAaA,CAAA,EAAG;MACd,OAAO,IAAI,CAACX,SAAS,CAACI,cAAc,CAACQ,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAK;QAC3D,OAAOD,KAAI,IAAKC,IAAI,CAACT,QAAO,IAAK,CAAC;MACpC,CAAC,EAAE,CAAC;IACN,CAAC;IAEDU,mBAAmBA,CAAA,EAAG;MACpB,OAAOC,IAAI,CAACC,SAAS,CAAC;QACpBrB,WAAW,EAAE,UAAU;QACvBC,aAAa,EAAE,OAAO;QACtBC,cAAc,EAAE,IAAI;QACpBS,UAAU,EAAE,sBAAsB;QAClCR,aAAa,EAAE;MACjB,CAAC,EAAE,IAAI,EAAE,CAAC;IACZ,CAAC;IAEDmB,gBAAgBA,CAAA,EAAG;MACjB,OAAOF,IAAI,CAACC,SAAS,CAAC;QACpBhB,YAAY,EAAE,oBAAoB;QAClCL,WAAW,EAAE,UAAU;QACvBM,UAAU,EAAE,sBAAsB;QAClCC,QAAQ,EAAE,sBAAsB;QAChCC,cAAc,EAAE;UACd,MAAM,EAAE,GAAG;UACX,IAAI,EAAE,GAAG;UACT,IAAI,EAAE;QACR,CAAC;QACDK,sBAAsB,EAAE;MAC1B,CAAC,EAAE,IAAI,EAAE,CAAC;IACZ;EACF,CAAC;EAEDU,OAAO,EAAE;IACPC,cAAcA,CAAA,EAAG;MACf,MAAMC,WAAU,GAAI;QAClB,GAAG,IAAI,CAAC1B,YAAY;QACpBY,UAAU,EAAE,IAAIe,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC;MAEA,IAAI,CAACjB,SAAS,CAACkB,IAAI,CAACH,WAAW;;MAE/B;MACA,IAAI,CAAC1B,YAAW,GAAI;QAClBC,WAAW,EAAE,EAAE;QACfC,aAAa,EAAE,EAAE;QACjBC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE;MACjB;MAEA0B,KAAK,CAAC,SAAS;IACjB,CAAC;IAEDC,eAAeA,CAAA,EAAG;MAChB;MACA,MAAMC,aAAY,GAAI,CAAC;MACvB,IAAI,CAAC3B,SAAS,CAACI,cAAc,CAACwB,OAAO,CAACd,IAAG,IAAK;QAC5C,IAAIA,IAAI,CAACtB,IAAG,IAAKsB,IAAI,CAACT,QAAQ,EAAE;UAC9BsB,aAAa,CAACb,IAAI,CAACtB,IAAI,IAAIsB,IAAI,CAACT,QAAO;QACzC;MACF,CAAC;MAED,MAAMwB,cAAa,GAAI;QACrB5B,YAAY,EAAE,IAAI,CAACD,SAAS,CAACC,YAAY;QACzCL,WAAW,EAAE,IAAI,CAACI,SAAS,CAACJ,WAAW;QACvCM,UAAU,EAAE,IAAI,CAACF,SAAS,CAACE,UAAU;QACrCC,QAAQ,EAAE,IAAI,CAACH,SAAS,CAACG,QAAQ;QACjCC,cAAc,EAAEuB,aAAa;QAC7BlB,sBAAsB,EAAE,IAAI,CAACE;MAC/B;MAEA,IAAI,CAACH,YAAY,CAACgB,IAAI,CAACK,cAAc;;MAErC;MACA,IAAI,CAAC7B,SAAQ,GAAI;QACfC,YAAY,EAAE,EAAE;QAChBL,WAAW,EAAE,EAAE;QACfM,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,EAAE;QACZC,cAAc,EAAE,CACd;UAAEZ,IAAI,EAAE,MAAM;UAAEa,QAAQ,EAAE;QAAE,CAAC,EAC7B;UAAEb,IAAI,EAAE,IAAI;UAAEa,QAAQ,EAAE;QAAE,CAAC,EAC3B;UAAEb,IAAI,EAAE,IAAI;UAAEa,QAAQ,EAAE;QAAE;MAE9B;MAEAoB,KAAK,CAAC,WAAW;IACnB,CAAC;IAEDK,OAAOA,CAAA,EAAG;MACR,IAAI,CAAC9B,SAAS,CAACI,cAAc,CAACoB,IAAI,CAAC;QAAEhC,IAAI,EAAE,EAAE;QAAEa,QAAQ,EAAE;MAAE,CAAC;IAC9D,CAAC;IAED0B,UAAUA,CAACC,KAAK,EAAE;MAChB,IAAI,CAAChC,SAAS,CAACI,cAAc,CAAC6B,MAAM,CAACD,KAAK,EAAE,CAAC;IAC/C,CAAC;IAEDE,eAAeA,CAACC,UAAU,EAAE;MAC1B,MAAMC,QAAO,GAAI,IAAI,CAAC9B,SAAS,CAAC+B,IAAI,CAACC,CAAA,IAAKA,CAAC,CAAC1C,WAAU,KAAMuC,UAAU;MACtE,OAAOC,QAAO,GAAIA,QAAQ,CAACvC,aAAY,GAAIsC,UAAS;IACtD,CAAC;IAEDI,UAAUA,CAACC,UAAU,EAAE;MACrB,OAAO,IAAIlB,IAAI,CAACkB,UAAU,CAAC,CAACC,cAAc,CAAC,OAAO;IACpD;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}