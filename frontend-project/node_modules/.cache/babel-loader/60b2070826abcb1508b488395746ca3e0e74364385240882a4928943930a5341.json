{"ast": null, "code": "import { createRouter, createWebHistory } from 'vue-router';\nimport Dashboard from '../views/Dashboard.vue';\nconst routes = [{\n  path: '/',\n  name: 'Dashboard',\n  component: Dashboard\n}];\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n});\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "Dashboard", "routes", "path", "name", "component", "router", "history", "process", "env", "BASE_URL"], "sources": ["/Users/<USER>/Desktop/dev/frontend-project/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\nimport Dashboard from '../views/Dashboard.vue'\n\nconst routes = [\n  {\n    path: '/',\n    name: 'Dashboard',\n    component: Dashboard\n  }\n]\n\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n})\n\nexport default router"], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D,OAAOC,SAAS,MAAM,wBAAwB;AAE9C,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEJ;AACb,CAAC,CACF;AAED,MAAMK,MAAM,GAAGP,YAAY,CAAC;EAC1BQ,OAAO,EAAEP,gBAAgB,CAACQ,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;EAC/CR;AACF,CAAC,CAAC;AAEF,eAAeI,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}