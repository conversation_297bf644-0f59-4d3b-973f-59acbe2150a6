{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nexport default {\n  name: 'EfficiencyMetrics',\n  data() {\n    return {\n      activeTab: 'efficiency',\n      activeChart: 'pd_trend',\n      metricTabs: [{\n        id: 'efficiency',\n        name: '效率指标'\n      }, {\n        id: 'cost',\n        name: '成本指标'\n      }],\n      chartOptions: [{\n        id: 'pd_trend',\n        name: 'PD投入趋势'\n      }, {\n        id: 'cost_trend',\n        name: '成本节约趋势'\n      }, {\n        id: 'build_time',\n        name: '构建时间趋势'\n      }, {\n        id: 'issue_rate',\n        name: '线上问题率趋势'\n      }],\n      months: ['1月', '2月', '3月', '4月', '5月', '6月'],\n      // 效率指标数据\n      efficiencyMetrics: {\n        level1: [{\n          id: 'pd_efficiency',\n          name: 'PD效率',\n          value: 0.85,\n          target: 0.9,\n          unit: '',\n          trend: 0.05,\n          description: '衡量前端基建在提升开发效率方面的综合表现',\n          source: '多维度统计数据',\n          status: 'good'\n        }, {\n          id: 'build_efficiency',\n          name: '构建效率',\n          value: 75,\n          target: 90,\n          unit: '%',\n          trend: 0.15,\n          description: '构建过程的效率提升百分比',\n          source: '构建系统数据',\n          status: 'good'\n        }, {\n          id: 'problem_resolution',\n          name: '问题解决效率',\n          value: 65,\n          target: 80,\n          unit: '%',\n          trend: -0.05,\n          description: '问题解决速度的提升百分比',\n          source: '问题跟踪系统',\n          status: 'warning'\n        }],\n        level2: [{\n          id: 'build_time',\n          name: '构建时间',\n          value: 3.5,\n          target: 2.5,\n          unit: '分钟',\n          trend: -0.8,\n          formula: '平均构建时间',\n          status: 'good'\n        }, {\n          id: 'dev_setup',\n          name: '开发环境搭建时间',\n          value: 15,\n          target: 10,\n          unit: '分钟',\n          trend: -5,\n          formula: '平均环境搭建时间',\n          status: 'good'\n        }, {\n          id: 'code_reuse',\n          name: '代码复用率',\n          value: 65,\n          target: 80,\n          unit: '%',\n          trend: 10,\n          formula: '复用代码行数 / 总代码行数',\n          status: 'good'\n        }, {\n          id: 'test_coverage',\n          name: '测试覆盖率',\n          value: 70,\n          target: 85,\n          unit: '%',\n          trend: 5,\n          formula: '测试覆盖的代码行数 / 总代码行数',\n          status: 'warning'\n        }, {\n          id: 'issue_resolution',\n          name: '问题解决时间',\n          value: 4.2,\n          target: 3,\n          unit: '小时',\n          trend: 0.2,\n          formula: '平均问题解决时间',\n          status: 'danger'\n        }, {\n          id: 'deployment_frequency',\n          name: '部署频率',\n          value: 3.5,\n          target: 5,\n          unit: '次/周',\n          trend: 0.5,\n          formula: '每周平均部署次数',\n          status: 'warning'\n        }]\n      },\n      // 成本指标数据\n      costMetrics: {\n        level1: [{\n          id: 'cost_saving',\n          name: '成本节约',\n          value: 25,\n          target: 30,\n          unit: '%',\n          trend: 5,\n          description: '前端基建带来的总体成本节约比例',\n          source: '财务数据与工时统计',\n          status: 'good'\n        }, {\n          id: 'resource_utilization',\n          name: '资源利用率',\n          value: 70,\n          target: 85,\n          unit: '%',\n          trend: 8,\n          description: '资源利用效率提升百分比',\n          source: '资源监控系统',\n          status: 'good'\n        }, {\n          id: 'maintenance_cost',\n          name: '维护成本',\n          value: 35,\n          target: 25,\n          unit: '%',\n          trend: -5,\n          description: '维护成本占总成本的百分比',\n          source: '工时与财务数据',\n          status: 'warning'\n        }],\n        level2: [{\n          id: 'labor_cost',\n          name: '人力成本',\n          value: 28,\n          target: 20,\n          unit: '%',\n          trend: -3,\n          formula: '人力成本 / 总成本',\n          status: 'warning'\n        }, {\n          id: 'infrastructure_cost',\n          name: '基础设施成本',\n          value: 15,\n          target: 12,\n          unit: '%',\n          trend: -2,\n          formula: '基础设施成本 / 总成本',\n          status: 'warning'\n        }, {\n          id: 'error_cost',\n          name: '错误修复成本',\n          value: 8,\n          target: 5,\n          unit: '%',\n          trend: -1.5,\n          formula: '错误修复成本 / 总成本',\n          status: 'warning'\n        }, {\n          id: 'time_to_market',\n          name: '上市时间',\n          value: 25,\n          target: 15,\n          unit: '天',\n          trend: -5,\n          formula: '平均产品上市时间',\n          status: 'good'\n        }, {\n          id: 'resource_saving',\n          name: '资源节约',\n          value: 30,\n          target: 40,\n          unit: '%',\n          trend: 8,\n          formula: '(旧资源使用 - 新资源使用) / 旧资源使用',\n          status: 'good'\n        }, {\n          id: 'tech_debt',\n          name: '技术债务',\n          value: 20,\n          target: 10,\n          unit: '%',\n          trend: -5,\n          formula: '技术债务工时 / 总工时',\n          status: 'warning'\n        }]\n      }\n    };\n  },\n  methods: {\n    formatValue(value) {\n      if (typeof value === 'number') {\n        return value % 1 === 0 ? value : value.toFixed(1);\n      }\n      return value;\n    },\n    formatTrend(trend) {\n      if (trend === 0) return '持平';\n      const prefix = trend > 0 ? '+' : '';\n      return `${prefix}${trend}%`;\n    },\n    getValueClass(metric, isCost = false) {\n      if (isCost) {\n        // 对于成本指标，值越低越好\n        if (metric.value <= metric.target) return 'value-good';\n        if (metric.value <= metric.target * 1.2) return 'value-warning';\n        return 'value-danger';\n      } else {\n        // 对于效率指标，值越高越好\n        if (metric.value >= metric.target) return 'value-good';\n        if (metric.value >= metric.target * 0.8) return 'value-warning';\n        return 'value-danger';\n      }\n    },\n    getTrendClass(trend, isCost = false) {\n      if (trend === 0) return 'trend-neutral';\n      if (isCost) {\n        // 对于成本指标，趋势下降是好的\n        return trend < 0 ? 'trend-good' : 'trend-bad';\n      } else {\n        // 对于效率指标，趋势上升是好的\n        return trend > 0 ? 'trend-good' : 'trend-bad';\n      }\n    },\n    getProgressWidth(metric, isCost = false) {\n      if (isCost) {\n        // 对于成本指标，值越低越好\n        const ratio = metric.target / Math.max(metric.value, metric.target);\n        return `${Math.min(ratio * 100, 100)}%`;\n      } else {\n        // 对于效率指标，值越高越好\n        const ratio = metric.value / Math.max(metric.value, metric.target);\n        return `${Math.min(ratio * 100, 100)}%`;\n      }\n    },\n    getActiveChartTitle() {\n      const chart = this.chartOptions.find(c => c.id === this.activeChart);\n      return chart ? chart.name : '';\n    },\n    getMockChartData() {\n      // 生成模拟图表数据\n      const data = [];\n      let color = '#3498db';\n      switch (this.activeChart) {\n        case 'pd_trend':\n          color = '#3498db';\n          data.push({\n            height: '40%',\n            color\n          }, {\n            height: '45%',\n            color\n          }, {\n            height: '55%',\n            color\n          }, {\n            height: '60%',\n            color\n          }, {\n            height: '70%',\n            color\n          }, {\n            height: '85%',\n            color\n          });\n          break;\n        case 'cost_trend':\n          color = '#e74c3c';\n          data.push({\n            height: '80%',\n            color\n          }, {\n            height: '75%',\n            color\n          }, {\n            height: '65%',\n            color\n          }, {\n            height: '55%',\n            color\n          }, {\n            height: '40%',\n            color\n          }, {\n            height: '25%',\n            color\n          });\n          break;\n        case 'build_time':\n          color = '#2ecc71';\n          data.push({\n            height: '90%',\n            color\n          }, {\n            height: '80%',\n            color\n          }, {\n            height: '70%',\n            color\n          }, {\n            height: '60%',\n            color\n          }, {\n            height: '50%',\n            color\n          }, {\n            height: '35%',\n            color\n          });\n          break;\n        case 'issue_rate':\n          color = '#f39c12';\n          data.push({\n            height: '75%',\n            color\n          }, {\n            height: '65%',\n            color\n          }, {\n            height: '70%',\n            color\n          }, {\n            height: '60%',\n            color\n          }, {\n            height: '50%',\n            color\n          }, {\n            height: '45%',\n            color\n          });\n          break;\n        default:\n          data.push({\n            height: '50%',\n            color\n          }, {\n            height: '60%',\n            color\n          }, {\n            height: '70%',\n            color\n          }, {\n            height: '65%',\n            color\n          }, {\n            height: '75%',\n            color\n          }, {\n            height: '80%',\n            color\n          });\n      }\n      return data;\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "activeTab", "activeChart", "metricTabs", "id", "chartOptions", "months", "efficiencyMetrics", "level1", "value", "target", "unit", "trend", "description", "source", "status", "level2", "formula", "costMetrics", "methods", "formatValue", "toFixed", "formatTrend", "prefix", "getValueClass", "metric", "isCost", "getTrendClass", "getProgressWidth", "ratio", "Math", "max", "min", "getActiveChartTitle", "chart", "find", "c", "getMockChartData", "color", "push", "height"], "sources": ["/Users/<USER>/Desktop/dev/frontend-project/src/components/EfficiencyMetrics.vue"], "sourcesContent": ["<template>\n  <div class=\"efficiency-metrics\">\n    <h2>效率与成本指标</h2>\n    \n    <!-- 指标选择器 -->\n    <div class=\"metrics-selector\">\n      <div class=\"selector-tabs\">\n        <button \n          v-for=\"tab in metricTabs\" \n          :key=\"tab.id\"\n          :class=\"['tab-btn', { active: activeTab === tab.id }]\"\n          @click=\"activeTab = tab.id\"\n        >\n          {{ tab.name }}\n        </button>\n      </div>\n    </div>\n    \n    <!-- 效率指标 -->\n    <div v-if=\"activeTab === 'efficiency'\" class=\"metrics-section\">\n      <div class=\"metrics-grid\">\n        <!-- 一级指标 -->\n        <div \n          v-for=\"metric in efficiencyMetrics.level1\" \n          :key=\"metric.id\"\n          class=\"metric-card level1\"\n        >\n          <div class=\"metric-header\">\n            <h3>{{ metric.name }}</h3>\n            <div class=\"metric-value\" :class=\"getValueClass(metric)\">\n              {{ formatValue(metric.value) }}\n              <span class=\"metric-unit\">{{ metric.unit }}</span>\n              <span \n                class=\"metric-trend\" \n                :class=\"getTrendClass(metric.trend)\"\n              >\n                {{ formatTrend(metric.trend) }}\n              </span>\n            </div>\n          </div>\n          <div class=\"metric-info\">\n            <div class=\"metric-desc\">{{ metric.description }}</div>\n            <div class=\"metric-source\">数据来源: {{ metric.source }}</div>\n          </div>\n          <div class=\"metric-target\">\n            <div class=\"target-bar\">\n              <div \n                class=\"target-progress\" \n                :style=\"{ width: getProgressWidth(metric) }\"\n              ></div>\n            </div>\n            <div class=\"target-text\">\n              目标值: {{ formatValue(metric.target) }} {{ metric.unit }}\n            </div>\n          </div>\n        </div>\n        \n        <!-- 二级指标 -->\n        <div class=\"level2-section\">\n          <h3>二级指标明细</h3>\n          <div class=\"level2-grid\">\n            <div \n              v-for=\"metric in efficiencyMetrics.level2\" \n              :key=\"metric.id\"\n              class=\"metric-card level2\"\n            >\n              <div class=\"metric-header\">\n                <h4>{{ metric.name }}</h4>\n                <div class=\"metric-value\" :class=\"getValueClass(metric)\">\n                  {{ formatValue(metric.value) }}\n                  <span class=\"metric-unit\">{{ metric.unit }}</span>\n                  <span \n                    class=\"metric-trend\" \n                    :class=\"getTrendClass(metric.trend)\"\n                  >\n                    {{ formatTrend(metric.trend) }}\n                  </span>\n                </div>\n              </div>\n              <div class=\"metric-formula\">\n                <span class=\"formula-label\">计算公式:</span> {{ metric.formula }}\n              </div>\n              <div class=\"metric-target\">\n                <div class=\"target-bar\">\n                  <div \n                    class=\"target-progress\" \n                    :style=\"{ width: getProgressWidth(metric) }\"\n                  ></div>\n                </div>\n                <div class=\"target-text\">\n                  目标值: {{ formatValue(metric.target) }} {{ metric.unit }}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 成本指标 -->\n    <div v-if=\"activeTab === 'cost'\" class=\"metrics-section\">\n      <div class=\"metrics-grid\">\n        <!-- 一级指标 -->\n        <div \n          v-for=\"metric in costMetrics.level1\" \n          :key=\"metric.id\"\n          class=\"metric-card level1\"\n        >\n          <div class=\"metric-header\">\n            <h3>{{ metric.name }}</h3>\n            <div class=\"metric-value\" :class=\"getValueClass(metric, true)\">\n              {{ formatValue(metric.value) }}\n              <span class=\"metric-unit\">{{ metric.unit }}</span>\n              <span \n                class=\"metric-trend\" \n                :class=\"getTrendClass(metric.trend, true)\"\n              >\n                {{ formatTrend(metric.trend) }}\n              </span>\n            </div>\n          </div>\n          <div class=\"metric-info\">\n            <div class=\"metric-desc\">{{ metric.description }}</div>\n            <div class=\"metric-source\">数据来源: {{ metric.source }}</div>\n          </div>\n          <div class=\"metric-target\">\n            <div class=\"target-bar\">\n              <div \n                class=\"target-progress\" \n                :style=\"{ width: getProgressWidth(metric, true) }\"\n              ></div>\n            </div>\n            <div class=\"target-text\">\n              目标值: {{ formatValue(metric.target) }} {{ metric.unit }}\n            </div>\n          </div>\n        </div>\n        \n        <!-- 二级指标 -->\n        <div class=\"level2-section\">\n          <h3>二级指标明细</h3>\n          <div class=\"level2-grid\">\n            <div \n              v-for=\"metric in costMetrics.level2\" \n              :key=\"metric.id\"\n              class=\"metric-card level2\"\n            >\n              <div class=\"metric-header\">\n                <h4>{{ metric.name }}</h4>\n                <div class=\"metric-value\" :class=\"getValueClass(metric, true)\">\n                  {{ formatValue(metric.value) }}\n                  <span class=\"metric-unit\">{{ metric.unit }}</span>\n                  <span \n                    class=\"metric-trend\" \n                    :class=\"getTrendClass(metric.trend, true)\"\n                  >\n                    {{ formatTrend(metric.trend) }}\n                  </span>\n                </div>\n              </div>\n              <div class=\"metric-formula\">\n                <span class=\"formula-label\">计算公式:</span> {{ metric.formula }}\n              </div>\n              <div class=\"metric-target\">\n                <div class=\"target-bar\">\n                  <div \n                    class=\"target-progress\" \n                    :style=\"{ width: getProgressWidth(metric, true) }\"\n                  ></div>\n                </div>\n                <div class=\"target-text\">\n                  目标值: {{ formatValue(metric.target) }} {{ metric.unit }}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 趋势图表 -->\n    <div class=\"trend-charts\">\n      <h3>指标趋势分析</h3>\n      <div class=\"chart-tabs\">\n        <button \n          v-for=\"chart in chartOptions\" \n          :key=\"chart.id\"\n          :class=\"['chart-tab', { active: activeChart === chart.id }]\"\n          @click=\"activeChart = chart.id\"\n        >\n          {{ chart.name }}\n        </button>\n      </div>\n      \n      <div class=\"chart-container\">\n        <!-- 这里可以集成图表库，如ECharts或Chart.js -->\n        <div class=\"chart-placeholder\">\n          <div class=\"chart-mock\">\n            <div class=\"chart-title\">{{ getActiveChartTitle() }}</div>\n            <div class=\"chart-mock-content\">\n              <div \n                v-for=\"(bar, index) in getMockChartData()\" \n                :key=\"index\"\n                class=\"mock-bar\"\n                :style=\"{ height: bar.height, backgroundColor: bar.color }\"\n              ></div>\n            </div>\n            <div class=\"chart-mock-labels\">\n              <span v-for=\"(month, index) in months\" :key=\"index\">{{ month }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'EfficiencyMetrics',\n  data() {\n    return {\n      activeTab: 'efficiency',\n      activeChart: 'pd_trend',\n      metricTabs: [\n        { id: 'efficiency', name: '效率指标' },\n        { id: 'cost', name: '成本指标' }\n      ],\n      chartOptions: [\n        { id: 'pd_trend', name: 'PD投入趋势' },\n        { id: 'cost_trend', name: '成本节约趋势' },\n        { id: 'build_time', name: '构建时间趋势' },\n        { id: 'issue_rate', name: '线上问题率趋势' }\n      ],\n      months: ['1月', '2月', '3月', '4月', '5月', '6月'],\n      \n      // 效率指标数据\n      efficiencyMetrics: {\n        level1: [\n          {\n            id: 'pd_efficiency',\n            name: 'PD效率',\n            value: 0.85,\n            target: 0.9,\n            unit: '',\n            trend: 0.05,\n            description: '衡量前端基建在提升开发效率方面的综合表现',\n            source: '多维度统计数据',\n            status: 'good'\n          },\n          {\n            id: 'build_efficiency',\n            name: '构建效率',\n            value: 75,\n            target: 90,\n            unit: '%',\n            trend: 0.15,\n            description: '构建过程的效率提升百分比',\n            source: '构建系统数据',\n            status: 'good'\n          },\n          {\n            id: 'problem_resolution',\n            name: '问题解决效率',\n            value: 65,\n            target: 80,\n            unit: '%',\n            trend: -0.05,\n            description: '问题解决速度的提升百分比',\n            source: '问题跟踪系统',\n            status: 'warning'\n          }\n        ],\n        level2: [\n          {\n            id: 'build_time',\n            name: '构建时间',\n            value: 3.5,\n            target: 2.5,\n            unit: '分钟',\n            trend: -0.8,\n            formula: '平均构建时间',\n            status: 'good'\n          },\n          {\n            id: 'dev_setup',\n            name: '开发环境搭建时间',\n            value: 15,\n            target: 10,\n            unit: '分钟',\n            trend: -5,\n            formula: '平均环境搭建时间',\n            status: 'good'\n          },\n          {\n            id: 'code_reuse',\n            name: '代码复用率',\n            value: 65,\n            target: 80,\n            unit: '%',\n            trend: 10,\n            formula: '复用代码行数 / 总代码行数',\n            status: 'good'\n          },\n          {\n            id: 'test_coverage',\n            name: '测试覆盖率',\n            value: 70,\n            target: 85,\n            unit: '%',\n            trend: 5,\n            formula: '测试覆盖的代码行数 / 总代码行数',\n            status: 'warning'\n          },\n          {\n            id: 'issue_resolution',\n            name: '问题解决时间',\n            value: 4.2,\n            target: 3,\n            unit: '小时',\n            trend: 0.2,\n            formula: '平均问题解决时间',\n            status: 'danger'\n          },\n          {\n            id: 'deployment_frequency',\n            name: '部署频率',\n            value: 3.5,\n            target: 5,\n            unit: '次/周',\n            trend: 0.5,\n            formula: '每周平均部署次数',\n            status: 'warning'\n          }\n        ]\n      },\n      \n      // 成本指标数据\n      costMetrics: {\n        level1: [\n          {\n            id: 'cost_saving',\n            name: '成本节约',\n            value: 25,\n            target: 30,\n            unit: '%',\n            trend: 5,\n            description: '前端基建带来的总体成本节约比例',\n            source: '财务数据与工时统计',\n            status: 'good'\n          },\n          {\n            id: 'resource_utilization',\n            name: '资源利用率',\n            value: 70,\n            target: 85,\n            unit: '%',\n            trend: 8,\n            description: '资源利用效率提升百分比',\n            source: '资源监控系统',\n            status: 'good'\n          },\n          {\n            id: 'maintenance_cost',\n            name: '维护成本',\n            value: 35,\n            target: 25,\n            unit: '%',\n            trend: -5,\n            description: '维护成本占总成本的百分比',\n            source: '工时与财务数据',\n            status: 'warning'\n          }\n        ],\n        level2: [\n          {\n            id: 'labor_cost',\n            name: '人力成本',\n            value: 28,\n            target: 20,\n            unit: '%',\n            trend: -3,\n            formula: '人力成本 / 总成本',\n            status: 'warning'\n          },\n          {\n            id: 'infrastructure_cost',\n            name: '基础设施成本',\n            value: 15,\n            target: 12,\n            unit: '%',\n            trend: -2,\n            formula: '基础设施成本 / 总成本',\n            status: 'warning'\n          },\n          {\n            id: 'error_cost',\n            name: '错误修复成本',\n            value: 8,\n            target: 5,\n            unit: '%',\n            trend: -1.5,\n            formula: '错误修复成本 / 总成本',\n            status: 'warning'\n          },\n          {\n            id: 'time_to_market',\n            name: '上市时间',\n            value: 25,\n            target: 15,\n            unit: '天',\n            trend: -5,\n            formula: '平均产品上市时间',\n            status: 'good'\n          },\n          {\n            id: 'resource_saving',\n            name: '资源节约',\n            value: 30,\n            target: 40,\n            unit: '%',\n            trend: 8,\n            formula: '(旧资源使用 - 新资源使用) / 旧资源使用',\n            status: 'good'\n          },\n          {\n            id: 'tech_debt',\n            name: '技术债务',\n            value: 20,\n            target: 10,\n            unit: '%',\n            trend: -5,\n            formula: '技术债务工时 / 总工时',\n            status: 'warning'\n          }\n        ]\n      }\n    }\n  },\n  \n  methods: {\n    formatValue(value) {\n      if (typeof value === 'number') {\n        return value % 1 === 0 ? value : value.toFixed(1)\n      }\n      return value\n    },\n    \n    formatTrend(trend) {\n      if (trend === 0) return '持平'\n      const prefix = trend > 0 ? '+' : ''\n      return `${prefix}${trend}%`\n    },\n    \n    getValueClass(metric, isCost = false) {\n      if (isCost) {\n        // 对于成本指标，值越低越好\n        if (metric.value <= metric.target) return 'value-good'\n        if (metric.value <= metric.target * 1.2) return 'value-warning'\n        return 'value-danger'\n      } else {\n        // 对于效率指标，值越高越好\n        if (metric.value >= metric.target) return 'value-good'\n        if (metric.value >= metric.target * 0.8) return 'value-warning'\n        return 'value-danger'\n      }\n    },\n    \n    getTrendClass(trend, isCost = false) {\n      if (trend === 0) return 'trend-neutral'\n      \n      if (isCost) {\n        // 对于成本指标，趋势下降是好的\n        return trend < 0 ? 'trend-good' : 'trend-bad'\n      } else {\n        // 对于效率指标，趋势上升是好的\n        return trend > 0 ? 'trend-good' : 'trend-bad'\n      }\n    },\n    \n    getProgressWidth(metric, isCost = false) {\n      if (isCost) {\n        // 对于成本指标，值越低越好\n        const ratio = metric.target / Math.max(metric.value, metric.target)\n        return `${Math.min(ratio * 100, 100)}%`\n      } else {\n        // 对于效率指标，值越高越好\n        const ratio = metric.value / Math.max(metric.value, metric.target)\n        return `${Math.min(ratio * 100, 100)}%`\n      }\n    },\n    \n    getActiveChartTitle() {\n      const chart = this.chartOptions.find(c => c.id === this.activeChart)\n      return chart ? chart.name : ''\n    },\n    \n    getMockChartData() {\n      // 生成模拟图表数据\n      const data = []\n      let color = '#3498db'\n      \n      switch (this.activeChart) {\n        case 'pd_trend':\n          color = '#3498db'\n          data.push(\n            { height: '40%', color },\n            { height: '45%', color },\n            { height: '55%', color },\n            { height: '60%', color },\n            { height: '70%', color },\n            { height: '85%', color }\n          )\n          break\n        case 'cost_trend':\n          color = '#e74c3c'\n          data.push(\n            { height: '80%', color },\n            { height: '75%', color },\n            { height: '65%', color },\n            { height: '55%', color },\n            { height: '40%', color },\n            { height: '25%', color }\n          )\n          break\n        case 'build_time':\n          color = '#2ecc71'\n          data.push(\n            { height: '90%', color },\n            { height: '80%', color },\n            { height: '70%', color },\n            { height: '60%', color },\n            { height: '50%', color },\n            { height: '35%', color }\n          )\n          break\n        case 'issue_rate':\n          color = '#f39c12'\n          data.push(\n            { height: '75%', color },\n            { height: '65%', color },\n            { height: '70%', color },\n            { height: '60%', color },\n            { height: '50%', color },\n            { height: '45%', color }\n          )\n          break\n        default:\n          data.push(\n            { height: '50%', color },\n            { height: '60%', color },\n            { height: '70%', color },\n            { height: '65%', color },\n            { height: '75%', color },\n            { height: '80%', color }\n          )\n      }\n      \n      return data\n    }\n  }\n}\n</script>\n\n<style scoped>\n.efficiency-metrics {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.efficiency-metrics h2 {\n  color: #2c3e50;\n  margin-bottom: 30px;\n  border-bottom: 2px solid #3498db;\n  padding-bottom: 10px;\n}\n\n.efficiency-metrics h3 {\n  color: #34495e;\n  margin-bottom: 20px;\n}\n\n/* 指标选择器样式 */\n.metrics-selector {\n  margin-bottom: 30px;\n}\n\n.selector-tabs {\n  display: flex;\n  gap: 10px;\n  border-bottom: 1px solid #e0e0e0;\n  padding-bottom: 10px;\n}\n\n.tab-btn {\n  background: #f5f5f5;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.3s ease;\n}\n\n.tab-btn.active {\n  background: #3498db;\n  color: white;\n}\n\n.tab-btn:hover:not(.active) {\n  background: #e0e0e0;\n}\n\n/* 指标卡片样式 */\n.metrics-grid {\n  display: flex;\n  flex-direction: column;\n  gap: 30px;\n}\n\n.metric-card {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n}\n\n.metric-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\n}\n\n.metric-card.level1 {\n  border-left: 4px solid #3498db;\n}\n\n.metric-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.metric-header h3,\n.metric-header h4 {\n  margin: 0;\n  color: #2c3e50;\n}\n\n.metric-value {\n  font-size: 1.5em;\n  font-weight: 700;\n  display: flex;\n  align-items: center;\n}\n\n.metric-unit {\n  font-size: 0.6em;\n  margin-left: 5px;\n  opacity: 0.7;\n}\n\n.metric-trend {\n  font-size: 0.6em;\n  margin-left: 10px;\n  padding: 3px 6px;\n  border-radius: 4px;\n}\n\n.value-good {\n  color: #2ecc71;\n}\n\n.value-warning {\n  color: #f39c12;\n}\n\n.value-danger {\n  color: #e74c3c;\n}\n\n.trend-good {\n  background: rgba(46, 204, 113, 0.2);\n  color: #27ae60;\n}\n\n.trend-bad {\n  background: rgba(231, 76, 60, 0.2);\n  color: #c0392b;\n}\n\n.trend-neutral {\n  background: rgba(149, 165, 166, 0.2);\n  color: #7f8c8d;\n}\n\n.metric-info {\n  margin-bottom: 15px;\n}\n\n.metric-desc {\n  color: #34495e;\n  margin-bottom: 5px;\n}\n\n.metric-source {\n  color: #7f8c8d;\n  font-size: 0.9em;\n}\n\n.metric-formula {\n  color: #34495e;\n  margin-bottom: 15px;\n  font-size: 0.9em;\n}\n\n.formula-label {\n  font-weight: 500;\n  color: #2c3e50;\n}\n\n/* 目标进度条样式 */\n.metric-target {\n  margin-top: 15px;\n}\n\n.target-bar {\n  height: 6px;\n  background: #ecf0f1;\n  border-radius: 3px;\n  overflow: hidden;\n  margin-bottom: 5px;\n}\n\n.target-progress {\n  height: 100%;\n  background: #3498db;\n  border-radius: 3px;\n}\n\n.target-text {\n  color: #7f8c8d;\n  font-size: 0.9em;\n}\n\n/* 二级指标样式 */\n.level2-section {\n  margin-top: 20px;\n}\n\n.level2-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 20px;\n}\n\n.metric-card.level2 {\n  border-left: 4px solid #2ecc71;\n}\n\n/* 趋势图表样式 */\n.trend-charts {\n  margin-top: 40px;\n}\n\n.chart-tabs {\n  display: flex;\n  gap: 10px;\n  margin-bottom: 20px;\n}\n\n.chart-tab {\n  background: #f5f5f5;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.3s ease;\n}\n\n.chart-tab.active {\n  background: #3498db;\n  color: white;\n}\n\n.chart-tab:hover:not(.active) {\n  background: #e0e0e0;\n}\n\n.chart-container {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n}\n\n/* 模拟图表样式 */\n.chart-placeholder {\n  height: 300px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.chart-mock {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.chart-title {\n  text-align: center;\n  margin-bottom: 20px;\n  color: #2c3e50;\n  font-weight: 500;\n}\n\n.chart-mock-content {\n  flex: 1;\n  display: flex;\n  align-items: flex-end;\n  justify-content: space-around;\n  padding: 0 20px;\n  border-bottom: 1px solid #e0e0e0;\n}\n\n.mock-bar {\n  width: 40px;\n  border-radius: 4px 4px 0 0;\n  transition: height 0.5s ease;\n}\n\n.chart-mock-labels {\n  display: flex;\n  justify-content: space-around;\n  padding: 10px 20px;\n}\n\n.chart-mock-labels span {\n  color: #7f8c8d;\n  font-size: 0.9em;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .level2-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .metric-header {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n  \n  .metric-value {\n    margin-top: 10px;\n  }\n  \n  .chart-mock-content {\n    padding: 0 10px;\n  }\n  \n  .mock-bar {\n    width: 30px;\n  }\n}\n</style>"], "mappings": ";;;AA0NA,eAAe;EACbA,IAAI,EAAE,mBAAmB;EACzBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,YAAY;MACvBC,WAAW,EAAE,UAAU;MACvBC,UAAU,EAAE,CACV;QAAEC,EAAE,EAAE,YAAY;QAAEL,IAAI,EAAE;MAAO,CAAC,EAClC;QAAEK,EAAE,EAAE,MAAM;QAAEL,IAAI,EAAE;MAAO,EAC5B;MACDM,YAAY,EAAE,CACZ;QAAED,EAAE,EAAE,UAAU;QAAEL,IAAI,EAAE;MAAS,CAAC,EAClC;QAAEK,EAAE,EAAE,YAAY;QAAEL,IAAI,EAAE;MAAS,CAAC,EACpC;QAAEK,EAAE,EAAE,YAAY;QAAEL,IAAI,EAAE;MAAS,CAAC,EACpC;QAAEK,EAAE,EAAE,YAAY;QAAEL,IAAI,EAAE;MAAU,EACrC;MACDO,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAE5C;MACAC,iBAAiB,EAAE;QACjBC,MAAM,EAAE,CACN;UACEJ,EAAE,EAAE,eAAe;UACnBL,IAAI,EAAE,MAAM;UACZU,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,GAAG;UACXC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,IAAI;UACXC,WAAW,EAAE,sBAAsB;UACnCC,MAAM,EAAE,SAAS;UACjBC,MAAM,EAAE;QACV,CAAC,EACD;UACEX,EAAE,EAAE,kBAAkB;UACtBL,IAAI,EAAE,MAAM;UACZU,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,IAAI,EAAE,GAAG;UACTC,KAAK,EAAE,IAAI;UACXC,WAAW,EAAE,cAAc;UAC3BC,MAAM,EAAE,QAAQ;UAChBC,MAAM,EAAE;QACV,CAAC,EACD;UACEX,EAAE,EAAE,oBAAoB;UACxBL,IAAI,EAAE,QAAQ;UACdU,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,IAAI,EAAE,GAAG;UACTC,KAAK,EAAE,CAAC,IAAI;UACZC,WAAW,EAAE,cAAc;UAC3BC,MAAM,EAAE,QAAQ;UAChBC,MAAM,EAAE;QACV,EACD;QACDC,MAAM,EAAE,CACN;UACEZ,EAAE,EAAE,YAAY;UAChBL,IAAI,EAAE,MAAM;UACZU,KAAK,EAAE,GAAG;UACVC,MAAM,EAAE,GAAG;UACXC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,CAAC,GAAG;UACXK,OAAO,EAAE,QAAQ;UACjBF,MAAM,EAAE;QACV,CAAC,EACD;UACEX,EAAE,EAAE,WAAW;UACfL,IAAI,EAAE,UAAU;UAChBU,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,CAAC,CAAC;UACTK,OAAO,EAAE,UAAU;UACnBF,MAAM,EAAE;QACV,CAAC,EACD;UACEX,EAAE,EAAE,YAAY;UAChBL,IAAI,EAAE,OAAO;UACbU,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,IAAI,EAAE,GAAG;UACTC,KAAK,EAAE,EAAE;UACTK,OAAO,EAAE,gBAAgB;UACzBF,MAAM,EAAE;QACV,CAAC,EACD;UACEX,EAAE,EAAE,eAAe;UACnBL,IAAI,EAAE,OAAO;UACbU,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,IAAI,EAAE,GAAG;UACTC,KAAK,EAAE,CAAC;UACRK,OAAO,EAAE,mBAAmB;UAC5BF,MAAM,EAAE;QACV,CAAC,EACD;UACEX,EAAE,EAAE,kBAAkB;UACtBL,IAAI,EAAE,QAAQ;UACdU,KAAK,EAAE,GAAG;UACVC,MAAM,EAAE,CAAC;UACTC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,GAAG;UACVK,OAAO,EAAE,UAAU;UACnBF,MAAM,EAAE;QACV,CAAC,EACD;UACEX,EAAE,EAAE,sBAAsB;UAC1BL,IAAI,EAAE,MAAM;UACZU,KAAK,EAAE,GAAG;UACVC,MAAM,EAAE,CAAC;UACTC,IAAI,EAAE,KAAK;UACXC,KAAK,EAAE,GAAG;UACVK,OAAO,EAAE,UAAU;UACnBF,MAAM,EAAE;QACV;MAEJ,CAAC;MAED;MACAG,WAAW,EAAE;QACXV,MAAM,EAAE,CACN;UACEJ,EAAE,EAAE,aAAa;UACjBL,IAAI,EAAE,MAAM;UACZU,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,IAAI,EAAE,GAAG;UACTC,KAAK,EAAE,CAAC;UACRC,WAAW,EAAE,iBAAiB;UAC9BC,MAAM,EAAE,WAAW;UACnBC,MAAM,EAAE;QACV,CAAC,EACD;UACEX,EAAE,EAAE,sBAAsB;UAC1BL,IAAI,EAAE,OAAO;UACbU,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,IAAI,EAAE,GAAG;UACTC,KAAK,EAAE,CAAC;UACRC,WAAW,EAAE,aAAa;UAC1BC,MAAM,EAAE,QAAQ;UAChBC,MAAM,EAAE;QACV,CAAC,EACD;UACEX,EAAE,EAAE,kBAAkB;UACtBL,IAAI,EAAE,MAAM;UACZU,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,IAAI,EAAE,GAAG;UACTC,KAAK,EAAE,CAAC,CAAC;UACTC,WAAW,EAAE,cAAc;UAC3BC,MAAM,EAAE,SAAS;UACjBC,MAAM,EAAE;QACV,EACD;QACDC,MAAM,EAAE,CACN;UACEZ,EAAE,EAAE,YAAY;UAChBL,IAAI,EAAE,MAAM;UACZU,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,IAAI,EAAE,GAAG;UACTC,KAAK,EAAE,CAAC,CAAC;UACTK,OAAO,EAAE,YAAY;UACrBF,MAAM,EAAE;QACV,CAAC,EACD;UACEX,EAAE,EAAE,qBAAqB;UACzBL,IAAI,EAAE,QAAQ;UACdU,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,IAAI,EAAE,GAAG;UACTC,KAAK,EAAE,CAAC,CAAC;UACTK,OAAO,EAAE,cAAc;UACvBF,MAAM,EAAE;QACV,CAAC,EACD;UACEX,EAAE,EAAE,YAAY;UAChBL,IAAI,EAAE,QAAQ;UACdU,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTC,IAAI,EAAE,GAAG;UACTC,KAAK,EAAE,CAAC,GAAG;UACXK,OAAO,EAAE,cAAc;UACvBF,MAAM,EAAE;QACV,CAAC,EACD;UACEX,EAAE,EAAE,gBAAgB;UACpBL,IAAI,EAAE,MAAM;UACZU,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,IAAI,EAAE,GAAG;UACTC,KAAK,EAAE,CAAC,CAAC;UACTK,OAAO,EAAE,UAAU;UACnBF,MAAM,EAAE;QACV,CAAC,EACD;UACEX,EAAE,EAAE,iBAAiB;UACrBL,IAAI,EAAE,MAAM;UACZU,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,IAAI,EAAE,GAAG;UACTC,KAAK,EAAE,CAAC;UACRK,OAAO,EAAE,yBAAyB;UAClCF,MAAM,EAAE;QACV,CAAC,EACD;UACEX,EAAE,EAAE,WAAW;UACfL,IAAI,EAAE,MAAM;UACZU,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,IAAI,EAAE,GAAG;UACTC,KAAK,EAAE,CAAC,CAAC;UACTK,OAAO,EAAE,cAAc;UACvBF,MAAM,EAAE;QACV;MAEJ;IACF;EACF,CAAC;EAEDI,OAAO,EAAE;IACPC,WAAWA,CAACX,KAAK,EAAE;MACjB,IAAI,OAAOA,KAAI,KAAM,QAAQ,EAAE;QAC7B,OAAOA,KAAI,GAAI,MAAM,IAAIA,KAAI,GAAIA,KAAK,CAACY,OAAO,CAAC,CAAC;MAClD;MACA,OAAOZ,KAAI;IACb,CAAC;IAEDa,WAAWA,CAACV,KAAK,EAAE;MACjB,IAAIA,KAAI,KAAM,CAAC,EAAE,OAAO,IAAG;MAC3B,MAAMW,MAAK,GAAIX,KAAI,GAAI,IAAI,GAAE,GAAI,EAAC;MAClC,OAAO,GAAGW,MAAM,GAAGX,KAAK,GAAE;IAC5B,CAAC;IAEDY,aAAaA,CAACC,MAAM,EAAEC,MAAK,GAAI,KAAK,EAAE;MACpC,IAAIA,MAAM,EAAE;QACV;QACA,IAAID,MAAM,CAAChB,KAAI,IAAKgB,MAAM,CAACf,MAAM,EAAE,OAAO,YAAW;QACrD,IAAIe,MAAM,CAAChB,KAAI,IAAKgB,MAAM,CAACf,MAAK,GAAI,GAAG,EAAE,OAAO,eAAc;QAC9D,OAAO,cAAa;MACtB,OAAO;QACL;QACA,IAAIe,MAAM,CAAChB,KAAI,IAAKgB,MAAM,CAACf,MAAM,EAAE,OAAO,YAAW;QACrD,IAAIe,MAAM,CAAChB,KAAI,IAAKgB,MAAM,CAACf,MAAK,GAAI,GAAG,EAAE,OAAO,eAAc;QAC9D,OAAO,cAAa;MACtB;IACF,CAAC;IAEDiB,aAAaA,CAACf,KAAK,EAAEc,MAAK,GAAI,KAAK,EAAE;MACnC,IAAId,KAAI,KAAM,CAAC,EAAE,OAAO,eAAc;MAEtC,IAAIc,MAAM,EAAE;QACV;QACA,OAAOd,KAAI,GAAI,IAAI,YAAW,GAAI,WAAU;MAC9C,OAAO;QACL;QACA,OAAOA,KAAI,GAAI,IAAI,YAAW,GAAI,WAAU;MAC9C;IACF,CAAC;IAEDgB,gBAAgBA,CAACH,MAAM,EAAEC,MAAK,GAAI,KAAK,EAAE;MACvC,IAAIA,MAAM,EAAE;QACV;QACA,MAAMG,KAAI,GAAIJ,MAAM,CAACf,MAAK,GAAIoB,IAAI,CAACC,GAAG,CAACN,MAAM,CAAChB,KAAK,EAAEgB,MAAM,CAACf,MAAM;QAClE,OAAO,GAAGoB,IAAI,CAACE,GAAG,CAACH,KAAI,GAAI,GAAG,EAAE,GAAG,CAAC,GAAE;MACxC,OAAO;QACL;QACA,MAAMA,KAAI,GAAIJ,MAAM,CAAChB,KAAI,GAAIqB,IAAI,CAACC,GAAG,CAACN,MAAM,CAAChB,KAAK,EAAEgB,MAAM,CAACf,MAAM;QACjE,OAAO,GAAGoB,IAAI,CAACE,GAAG,CAACH,KAAI,GAAI,GAAG,EAAE,GAAG,CAAC,GAAE;MACxC;IACF,CAAC;IAEDI,mBAAmBA,CAAA,EAAG;MACpB,MAAMC,KAAI,GAAI,IAAI,CAAC7B,YAAY,CAAC8B,IAAI,CAACC,CAAA,IAAKA,CAAC,CAAChC,EAAC,KAAM,IAAI,CAACF,WAAW;MACnE,OAAOgC,KAAI,GAAIA,KAAK,CAACnC,IAAG,GAAI,EAAC;IAC/B,CAAC;IAEDsC,gBAAgBA,CAAA,EAAG;MACjB;MACA,MAAMrC,IAAG,GAAI,EAAC;MACd,IAAIsC,KAAI,GAAI,SAAQ;MAEpB,QAAQ,IAAI,CAACpC,WAAW;QACtB,KAAK,UAAU;UACboC,KAAI,GAAI,SAAQ;UAChBtC,IAAI,CAACuC,IAAI,CACP;YAAEC,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CACzB;UACA;QACF,KAAK,YAAY;UACfA,KAAI,GAAI,SAAQ;UAChBtC,IAAI,CAACuC,IAAI,CACP;YAAEC,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CACzB;UACA;QACF,KAAK,YAAY;UACfA,KAAI,GAAI,SAAQ;UAChBtC,IAAI,CAACuC,IAAI,CACP;YAAEC,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CACzB;UACA;QACF,KAAK,YAAY;UACfA,KAAI,GAAI,SAAQ;UAChBtC,IAAI,CAACuC,IAAI,CACP;YAAEC,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CACzB;UACA;QACF;UACEtC,IAAI,CAACuC,IAAI,CACP;YAAEC,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CAAC,EACxB;YAAEE,MAAM,EAAE,KAAK;YAAEF;UAAM,CACzB;MACJ;MAEA,OAAOtC,IAAG;IACZ;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}