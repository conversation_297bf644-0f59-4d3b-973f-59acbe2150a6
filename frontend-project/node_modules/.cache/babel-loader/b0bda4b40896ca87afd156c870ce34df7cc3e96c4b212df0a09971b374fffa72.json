{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeStyle as _normalizeStyle, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard\"\n};\nconst _hoisted_2 = {\n  class: \"overview-section\"\n};\nconst _hoisted_3 = {\n  class: \"metric-cards\"\n};\nconst _hoisted_4 = {\n  class: \"metric-card efficiency\"\n};\nconst _hoisted_5 = {\n  class: \"metric-content\"\n};\nconst _hoisted_6 = {\n  class: \"metric-value\"\n};\nconst _hoisted_7 = {\n  class: \"metric-trend positive\"\n};\nconst _hoisted_8 = {\n  class: \"metric-card cost\"\n};\nconst _hoisted_9 = {\n  class: \"metric-content\"\n};\nconst _hoisted_10 = {\n  class: \"metric-value\"\n};\nconst _hoisted_11 = {\n  class: \"metric-trend positive\"\n};\nconst _hoisted_12 = {\n  class: \"efficiency-section\"\n};\nconst _hoisted_13 = {\n  class: \"level-one-metrics\"\n};\nconst _hoisted_14 = {\n  class: \"metrics-grid\"\n};\nconst _hoisted_15 = {\n  class: \"metric-header\"\n};\nconst _hoisted_16 = {\n  class: \"metric-name\"\n};\nconst _hoisted_17 = {\n  class: \"metric-target\"\n};\nconst _hoisted_18 = {\n  class: \"metric-progress\"\n};\nconst _hoisted_19 = {\n  class: \"progress-bar\"\n};\nconst _hoisted_20 = {\n  class: \"progress-text\"\n};\nconst _hoisted_21 = {\n  class: \"metric-details\"\n};\nconst _hoisted_22 = {\n  class: \"data-source\"\n};\nconst _hoisted_23 = {\n  class: \"level-two-metrics\"\n};\nconst _hoisted_24 = {\n  class: \"metric-category\"\n};\nconst _hoisted_25 = {\n  class: \"metrics-table\"\n};\nconst _hoisted_26 = {\n  class: \"metric-category\"\n};\nconst _hoisted_27 = {\n  class: \"metrics-table\"\n};\nconst _hoisted_28 = {\n  class: \"metric-category\"\n};\nconst _hoisted_29 = {\n  class: \"metrics-table\"\n};\nconst _hoisted_30 = {\n  class: \"cost-section\"\n};\nconst _hoisted_31 = {\n  class: \"cost-overview\"\n};\nconst _hoisted_32 = {\n  class: \"cost-metrics-grid\"\n};\nconst _hoisted_33 = {\n  class: \"cost-metric-header\"\n};\nconst _hoisted_34 = {\n  class: \"cost-target\"\n};\nconst _hoisted_35 = {\n  class: \"cost-metric-body\"\n};\nconst _hoisted_36 = {\n  class: \"cost-value\"\n};\nconst _hoisted_37 = {\n  class: \"current-value\"\n};\nconst _hoisted_38 = {\n  class: \"cost-progress\"\n};\nconst _hoisted_39 = {\n  class: \"progress-bar\"\n};\nconst _hoisted_40 = {\n  class: \"cost-source\"\n};\nconst _hoisted_41 = {\n  class: \"cost-detail\"\n};\nconst _hoisted_42 = {\n  class: \"cost-detail-table\"\n};\nconst _hoisted_43 = {\n  class: \"key-metrics-section\"\n};\nconst _hoisted_44 = {\n  class: \"key-metrics-grid\"\n};\nconst _hoisted_45 = {\n  class: \"key-metric-content\"\n};\nconst _hoisted_46 = {\n  class: \"key-metric-value\"\n};\nconst _hoisted_47 = {\n  class: \"key-metric-target\"\n};\nconst _hoisted_48 = {\n  class: \"key-metric-source\"\n};\nconst _hoisted_49 = {\n  class: \"data-collection-section\"\n};\nconst _hoisted_50 = {\n  class: \"collection-status\"\n};\nconst _hoisted_51 = {\n  class: \"source-name\"\n};\nconst _hoisted_52 = {\n  class: \"last-update\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 页面标题 \"), _cache[21] || (_cache[21] = _createElementVNode(\"div\", {\n    class: \"page-header\"\n  }, [_createElementVNode(\"h1\", null, \"前端基建效率与成本洞察平台\"), _createElementVNode(\"p\", {\n    class: \"subtitle\"\n  }, \"数据驱动的基础设施优化效果量化分析\")], -1 /* CACHED */)), _createCommentVNode(\" 北极星指标概览 \"), _createElementVNode(\"div\", _hoisted_2, [_cache[4] || (_cache[4] = _createElementVNode(\"h2\", null, \"北极星指标\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n    class: \"metric-icon\"\n  }, \"⚡\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_5, [_cache[0] || (_cache[0] = _createElementVNode(\"h3\", null, \"累计节约人天数\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_6, _toDisplayString($data.totalPDSaved) + \" PD\", 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_7, \"+\" + _toDisplayString($data.monthlyPDSaved) + \" PD/月\", 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_8, [_cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n    class: \"metric-icon\"\n  }, \"💰\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_9, [_cache[2] || (_cache[2] = _createElementVNode(\"h3\", null, \"累计成本节约\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_10, \"¥\" + _toDisplayString($data.totalCostSaved.toLocaleString()), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_11, \"+¥\" + _toDisplayString($data.monthlyCostSaved.toLocaleString()) + \"/月\", 1 /* TEXT */)])])])]), _createCommentVNode(\" 提效维度统计 \"), _createElementVNode(\"div\", _hoisted_12, [_cache[13] || (_cache[13] = _createElementVNode(\"h2\", null, \"提效维度统计\", -1 /* CACHED */)), _createCommentVNode(\" 一级过程指标 \"), _createElementVNode(\"div\", _hoisted_13, [_cache[5] || (_cache[5] = _createElementVNode(\"h3\", null, \"一级过程指标\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_14, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.efficiencyL1Metrics, metric => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"metric-item\",\n      key: metric.name\n    }, [_createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"span\", _hoisted_16, _toDisplayString(metric.name), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_17, \"目标: \" + _toDisplayString(metric.target), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", {\n      class: \"progress-fill\",\n      style: _normalizeStyle({\n        width: metric.progress + '%'\n      })\n    }, null, 4 /* STYLE */)]), _createElementVNode(\"span\", _hoisted_20, _toDisplayString(metric.current) + \" / \" + _toDisplayString(metric.target), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"span\", _hoisted_22, \"数据来源: \" + _toDisplayString(metric.dataSource), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 二级过程指标 \"), _createElementVNode(\"div\", _hoisted_23, [_cache[12] || (_cache[12] = _createElementVNode(\"h3\", null, \"二级过程指标\", -1 /* CACHED */)), _createCommentVNode(\" 工程构建类 \"), _createElementVNode(\"div\", _hoisted_24, [_cache[7] || (_cache[7] = _createElementVNode(\"h4\", null, \"工程构建类\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"table\", null, [_cache[6] || (_cache[6] = _createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, \"指标名称\"), _createElementVNode(\"th\", null, \"当前值\"), _createElementVNode(\"th\", null, \"目标值\"), _createElementVNode(\"th\", null, \"完成率\"), _createElementVNode(\"th\", null, \"数据来源\")])], -1 /* CACHED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.buildMetrics, metric => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: metric.name\n    }, [_createElementVNode(\"td\", null, _toDisplayString(metric.name), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(metric.current), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(metric.target), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"span\", {\n      class: _normalizeClass(['completion-rate', metric.status])\n    }, _toDisplayString(metric.completionRate) + \"% \", 3 /* TEXT, CLASS */)]), _createElementVNode(\"td\", null, _toDisplayString(metric.dataSource), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))])])])]), _createCommentVNode(\" 线上问题处理类 \"), _createElementVNode(\"div\", _hoisted_26, [_cache[9] || (_cache[9] = _createElementVNode(\"h4\", null, \"线上问题处理类\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"table\", null, [_cache[8] || (_cache[8] = _createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, \"指标名称\"), _createElementVNode(\"th\", null, \"当前值\"), _createElementVNode(\"th\", null, \"目标值\"), _createElementVNode(\"th\", null, \"完成率\"), _createElementVNode(\"th\", null, \"数据来源\")])], -1 /* CACHED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.problemMetrics, metric => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: metric.name\n    }, [_createElementVNode(\"td\", null, _toDisplayString(metric.name), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(metric.current), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(metric.target), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"span\", {\n      class: _normalizeClass(['completion-rate', metric.status])\n    }, _toDisplayString(metric.completionRate) + \"% \", 3 /* TEXT, CLASS */)]), _createElementVNode(\"td\", null, _toDisplayString(metric.dataSource), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))])])])]), _createCommentVNode(\" 组件复用类 \"), _createElementVNode(\"div\", _hoisted_28, [_cache[11] || (_cache[11] = _createElementVNode(\"h4\", null, \"组件复用类\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"table\", null, [_cache[10] || (_cache[10] = _createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, \"指标名称\"), _createElementVNode(\"th\", null, \"当前值\"), _createElementVNode(\"th\", null, \"目标值\"), _createElementVNode(\"th\", null, \"完成率\"), _createElementVNode(\"th\", null, \"数据来源\")])], -1 /* CACHED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.componentMetrics, metric => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: metric.name\n    }, [_createElementVNode(\"td\", null, _toDisplayString(metric.name), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(metric.current), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(metric.target), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"span\", {\n      class: _normalizeClass(['completion-rate', metric.status])\n    }, _toDisplayString(metric.completionRate) + \"% \", 3 /* TEXT, CLASS */)]), _createElementVNode(\"td\", null, _toDisplayString(metric.dataSource), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))])])])])])]), _createCommentVNode(\" 成本维度统计 \"), _createElementVNode(\"div\", _hoisted_30, [_cache[17] || (_cache[17] = _createElementVNode(\"h2\", null, \"成本维度统计\", -1 /* CACHED */)), _createCommentVNode(\" 一级过程指标 \"), _createElementVNode(\"div\", _hoisted_31, [_cache[14] || (_cache[14] = _createElementVNode(\"h3\", null, \"一级过程指标\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_32, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.costL1Metrics, metric => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"cost-metric-card\",\n      key: metric.name\n    }, [_createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"h4\", null, _toDisplayString(metric.name), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_34, \"目标: \" + _toDisplayString(metric.target), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_35, [_createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"span\", _hoisted_37, \"¥\" + _toDisplayString(metric.currentSaving.toLocaleString()), 1 /* TEXT */), _createElementVNode(\"span\", {\n      class: _normalizeClass([\"optimization-rate\", metric.status])\n    }, _toDisplayString(metric.optimizationRate) + \"% \", 3 /* TEXT, CLASS */)]), _createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"div\", _hoisted_39, [_createElementVNode(\"div\", {\n      class: \"progress-fill\",\n      style: _normalizeStyle({\n        width: metric.progress + '%'\n      })\n    }, null, 4 /* STYLE */)])]), _createElementVNode(\"div\", _hoisted_40, _toDisplayString(metric.dataSource), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 二级过程指标 \"), _createElementVNode(\"div\", _hoisted_41, [_cache[16] || (_cache[16] = _createElementVNode(\"h3\", null, \"二级过程指标\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_42, [_createElementVNode(\"table\", null, [_cache[15] || (_cache[15] = _createElementVNode(\"thead\", null, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, \"指标名称\"), _createElementVNode(\"th\", null, \"当前节约金额\"), _createElementVNode(\"th\", null, \"目标值\"), _createElementVNode(\"th\", null, \"完成率\"), _createElementVNode(\"th\", null, \"数据来源\"), _createElementVNode(\"th\", null, \"趋势\")])], -1 /* CACHED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.costL2Metrics, metric => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: metric.name\n    }, [_createElementVNode(\"td\", null, _toDisplayString(metric.name), 1 /* TEXT */), _createElementVNode(\"td\", null, \"¥\" + _toDisplayString(metric.currentSaving.toLocaleString()), 1 /* TEXT */), _createElementVNode(\"td\", null, _toDisplayString(metric.target), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"span\", {\n      class: _normalizeClass(['completion-rate', metric.status])\n    }, _toDisplayString(metric.completionRate) + \"% \", 3 /* TEXT, CLASS */)]), _createElementVNode(\"td\", null, _toDisplayString(metric.dataSource), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"span\", {\n      class: _normalizeClass(['trend', metric.trend])\n    }, _toDisplayString(metric.trendText), 3 /* TEXT, CLASS */)])]);\n  }), 128 /* KEYED_FRAGMENT */))])])])])]), _createCommentVNode(\" 重点执行指标 \"), _createElementVNode(\"div\", _hoisted_43, [_cache[19] || (_cache[19] = _createElementVNode(\"h2\", null, \"重点执行指标\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_44, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.keyMetrics, metric => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"key-metric-card\",\n      key: metric.name\n    }, [_cache[18] || (_cache[18] = _createElementVNode(\"div\", {\n      class: \"key-metric-icon\"\n    }, \"📊\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_45, [_createElementVNode(\"h4\", null, _toDisplayString(metric.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_46, _toDisplayString(metric.current), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_47, \"目标: \" + _toDisplayString(metric.target), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_48, _toDisplayString(metric.dataSource), 1 /* TEXT */)]), _createElementVNode(\"div\", {\n      class: _normalizeClass([\"key-metric-status\", metric.status])\n    }, _toDisplayString(metric.statusText), 3 /* TEXT, CLASS */)]);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 数据采集状态 \"), _createElementVNode(\"div\", _hoisted_49, [_cache[20] || (_cache[20] = _createElementVNode(\"h2\", null, \"数据采集状态\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_50, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.dataSources, source => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      class: \"status-item\",\n      key: source.name\n    }, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"status-indicator\", source.status])\n    }, null, 2 /* CLASS */), _createElementVNode(\"span\", _hoisted_51, _toDisplayString(source.name), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_52, \"最后更新: \" + _toDisplayString(source.lastUpdate), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_toDisplayString", "$data", "totalPDSaved", "_hoisted_7", "monthlyPDSaved", "_hoisted_8", "_hoisted_9", "_hoisted_10", "totalCostSaved", "toLocaleString", "_hoisted_11", "monthlyCostSaved", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_Fragment", "_renderList", "efficiencyL1Metrics", "metric", "key", "name", "_hoisted_15", "_hoisted_16", "_hoisted_17", "target", "_hoisted_18", "_hoisted_19", "style", "_normalizeStyle", "width", "progress", "_hoisted_20", "current", "_hoisted_21", "_hoisted_22", "dataSource", "_hoisted_23", "_hoisted_24", "_hoisted_25", "buildMetrics", "_normalizeClass", "status", "completionRate", "_hoisted_26", "_hoisted_27", "problemMetrics", "_hoisted_28", "_hoisted_29", "componentMetrics", "_hoisted_30", "_hoisted_31", "_hoisted_32", "costL1Metrics", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "_hoisted_37", "currentSaving", "optimizationRate", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "costL2Metrics", "trend", "trendText", "_hoisted_43", "_hoisted_44", "keyMetrics", "_hoisted_45", "_hoisted_46", "_hoisted_47", "_hoisted_48", "statusText", "_hoisted_49", "_hoisted_50", "dataSources", "source", "_hoisted_51", "_hoisted_52", "lastUpdate"], "sources": ["/Users/<USER>/Desktop/dev/frontend-project/src/views/Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h1>前端基建效率与成本洞察平台</h1>\n      <p class=\"subtitle\">数据驱动的基础设施优化效果量化分析</p>\n    </div>\n\n    <!-- 北极星指标概览 -->\n    <div class=\"overview-section\">\n      <h2>北极星指标</h2>\n      <div class=\"metric-cards\">\n        <div class=\"metric-card efficiency\">\n          <div class=\"metric-icon\">⚡</div>\n          <div class=\"metric-content\">\n            <h3>累计节约人天数</h3>\n            <div class=\"metric-value\">{{ totalPDSaved }} PD</div>\n            <div class=\"metric-trend positive\">+{{ monthlyPDSaved }} PD/月</div>\n          </div>\n        </div>\n        <div class=\"metric-card cost\">\n          <div class=\"metric-icon\">💰</div>\n          <div class=\"metric-content\">\n            <h3>累计成本节约</h3>\n            <div class=\"metric-value\">¥{{ totalCostSaved.toLocaleString() }}</div>\n            <div class=\"metric-trend positive\">+¥{{ monthlyCostSaved.toLocaleString() }}/月</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 提效维度统计 -->\n    <div class=\"efficiency-section\">\n      <h2>提效维度统计</h2>\n      \n      <!-- 一级过程指标 -->\n      <div class=\"level-one-metrics\">\n        <h3>一级过程指标</h3>\n        <div class=\"metrics-grid\">\n          <div class=\"metric-item\" v-for=\"metric in efficiencyL1Metrics\" :key=\"metric.name\">\n            <div class=\"metric-header\">\n              <span class=\"metric-name\">{{ metric.name }}</span>\n              <span class=\"metric-target\">目标: {{ metric.target }}</span>\n            </div>\n            <div class=\"metric-progress\">\n              <div class=\"progress-bar\">\n                <div class=\"progress-fill\" :style=\"{width: metric.progress + '%'}\"></div>\n              </div>\n              <span class=\"progress-text\">{{ metric.current }} / {{ metric.target }}</span>\n            </div>\n            <div class=\"metric-details\">\n              <span class=\"data-source\">数据来源: {{ metric.dataSource }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 二级过程指标 -->\n      <div class=\"level-two-metrics\">\n        <h3>二级过程指标</h3>\n        \n        <!-- 工程构建类 -->\n        <div class=\"metric-category\">\n          <h4>工程构建类</h4>\n          <div class=\"metrics-table\">\n            <table>\n              <thead>\n                <tr>\n                  <th>指标名称</th>\n                  <th>当前值</th>\n                  <th>目标值</th>\n                  <th>完成率</th>\n                  <th>数据来源</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr v-for=\"metric in buildMetrics\" :key=\"metric.name\">\n                  <td>{{ metric.name }}</td>\n                  <td>{{ metric.current }}</td>\n                  <td>{{ metric.target }}</td>\n                  <td>\n                    <span :class=\"['completion-rate', metric.status]\">\n                      {{ metric.completionRate }}%\n                    </span>\n                  </td>\n                  <td>{{ metric.dataSource }}</td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        <!-- 线上问题处理类 -->\n        <div class=\"metric-category\">\n          <h4>线上问题处理类</h4>\n          <div class=\"metrics-table\">\n            <table>\n              <thead>\n                <tr>\n                  <th>指标名称</th>\n                  <th>当前值</th>\n                  <th>目标值</th>\n                  <th>完成率</th>\n                  <th>数据来源</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr v-for=\"metric in problemMetrics\" :key=\"metric.name\">\n                  <td>{{ metric.name }}</td>\n                  <td>{{ metric.current }}</td>\n                  <td>{{ metric.target }}</td>\n                  <td>\n                    <span :class=\"['completion-rate', metric.status]\">\n                      {{ metric.completionRate }}%\n                    </span>\n                  </td>\n                  <td>{{ metric.dataSource }}</td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        <!-- 组件复用类 -->\n        <div class=\"metric-category\">\n          <h4>组件复用类</h4>\n          <div class=\"metrics-table\">\n            <table>\n              <thead>\n                <tr>\n                  <th>指标名称</th>\n                  <th>当前值</th>\n                  <th>目标值</th>\n                  <th>完成率</th>\n                  <th>数据来源</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr v-for=\"metric in componentMetrics\" :key=\"metric.name\">\n                  <td>{{ metric.name }}</td>\n                  <td>{{ metric.current }}</td>\n                  <td>{{ metric.target }}</td>\n                  <td>\n                    <span :class=\"['completion-rate', metric.status]\">\n                      {{ metric.completionRate }}%\n                    </span>\n                  </td>\n                  <td>{{ metric.dataSource }}</td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 成本维度统计 -->\n    <div class=\"cost-section\">\n      <h2>成本维度统计</h2>\n      \n      <!-- 一级过程指标 -->\n      <div class=\"cost-overview\">\n        <h3>一级过程指标</h3>\n        <div class=\"cost-metrics-grid\">\n          <div class=\"cost-metric-card\" v-for=\"metric in costL1Metrics\" :key=\"metric.name\">\n            <div class=\"cost-metric-header\">\n              <h4>{{ metric.name }}</h4>\n              <span class=\"cost-target\">目标: {{ metric.target }}</span>\n            </div>\n            <div class=\"cost-metric-body\">\n              <div class=\"cost-value\">\n                <span class=\"current-value\">¥{{ metric.currentSaving.toLocaleString() }}</span>\n                <span class=\"optimization-rate\" :class=\"metric.status\">\n                  {{ metric.optimizationRate }}%\n                </span>\n              </div>\n              <div class=\"cost-progress\">\n                <div class=\"progress-bar\">\n                  <div class=\"progress-fill\" :style=\"{width: metric.progress + '%'}\"></div>\n                </div>\n              </div>\n              <div class=\"cost-source\">{{ metric.dataSource }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 二级过程指标 -->\n      <div class=\"cost-detail\">\n        <h3>二级过程指标</h3>\n        <div class=\"cost-detail-table\">\n          <table>\n            <thead>\n              <tr>\n                <th>指标名称</th>\n                <th>当前节约金额</th>\n                <th>目标值</th>\n                <th>完成率</th>\n                <th>数据来源</th>\n                <th>趋势</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr v-for=\"metric in costL2Metrics\" :key=\"metric.name\">\n                <td>{{ metric.name }}</td>\n                <td>¥{{ metric.currentSaving.toLocaleString() }}</td>\n                <td>{{ metric.target }}</td>\n                <td>\n                  <span :class=\"['completion-rate', metric.status]\">\n                    {{ metric.completionRate }}%\n                  </span>\n                </td>\n                <td>{{ metric.dataSource }}</td>\n                <td>\n                  <span :class=\"['trend', metric.trend]\">\n                    {{ metric.trendText }}\n                  </span>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n\n    <!-- 重点执行指标 -->\n    <div class=\"key-metrics-section\">\n      <h2>重点执行指标</h2>\n      <div class=\"key-metrics-grid\">\n        <div class=\"key-metric-card\" v-for=\"metric in keyMetrics\" :key=\"metric.name\">\n          <div class=\"key-metric-icon\">📊</div>\n          <div class=\"key-metric-content\">\n            <h4>{{ metric.name }}</h4>\n            <div class=\"key-metric-value\">{{ metric.current }}</div>\n            <div class=\"key-metric-target\">目标: {{ metric.target }}</div>\n            <div class=\"key-metric-source\">{{ metric.dataSource }}</div>\n          </div>\n          <div class=\"key-metric-status\" :class=\"metric.status\">\n            {{ metric.statusText }}\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 数据采集状态 -->\n    <div class=\"data-collection-section\">\n      <h2>数据采集状态</h2>\n      <div class=\"collection-status\">\n        <div class=\"status-item\" v-for=\"source in dataSources\" :key=\"source.name\">\n          <div class=\"status-indicator\" :class=\"source.status\"></div>\n          <span class=\"source-name\">{{ source.name }}</span>\n          <span class=\"last-update\">最后更新: {{ source.lastUpdate }}</span>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Dashboard',\n  data() {\n    return {\n      // 北极星指标\n      totalPDSaved: 156.5,\n      monthlyPDSaved: 28.3,\n      totalCostSaved: 1250000,\n      monthlyCostSaved: 185000,\n      \n      // 提效维度一级指标\n      efficiencyL1Metrics: [\n        {\n          name: '构建提效PD',\n          current: '22.5 PD/月',\n          target: '>20 PD/月',\n          progress: 112,\n          dataSource: '构建系统、部署平台'\n        },\n        {\n          name: '问题提效PD',\n          current: '18.2 PD/月',\n          target: '>15 PD/月',\n          progress: 121,\n          dataSource: '监控系统、工单系统'\n        },\n        {\n          name: '组件提效PD',\n          current: '12.8 PD/月',\n          target: '>10 PD/月',\n          progress: 128,\n          dataSource: '代码仓库、组件库'\n        }\n      ],\n      \n      // 工程构建类指标\n      buildMetrics: [\n        {\n          name: '构建速度PD',\n          current: '9.2 PD/月',\n          target: '>8 PD/月',\n          completionRate: 115,\n          status: 'success',\n          dataSource: '构建系统日志'\n        },\n        {\n          name: '部署效率PD',\n          current: '7.8 PD/月',\n          target: '>6 PD/月',\n          completionRate: 130,\n          status: 'success',\n          dataSource: '部署平台日志'\n        },\n        {\n          name: '开发体验PD',\n          current: '5.5 PD/月',\n          target: '>4 PD/月',\n          completionRate: 137,\n          status: 'success',\n          dataSource: 'IDE插件统计'\n        }\n      ],\n      \n      // 线上问题处理类指标\n      problemMetrics: [\n        {\n          name: '发现提效PD',\n          current: '6.2 PD/月',\n          target: '>5 PD/月',\n          completionRate: 124,\n          status: 'success',\n          dataSource: '监控系统'\n        },\n        {\n          name: '定位提效PD',\n          current: '9.5 PD/月',\n          target: '>8 PD/月',\n          completionRate: 118,\n          status: 'success',\n          dataSource: '工单系统'\n        },\n        {\n          name: '解决提效PD',\n          current: '2.5 PD/月',\n          target: '>3 PD/月',\n          completionRate: 83,\n          status: 'warning',\n          dataSource: '工单系统'\n        }\n      ],\n      \n      // 组件复用类指标\n      componentMetrics: [\n        {\n          name: '开发提效PD',\n          current: '4.2 PD/月',\n          target: '>3 PD/月',\n          completionRate: 140,\n          status: 'success',\n          dataSource: '项目管理工具'\n        },\n        {\n          name: '复用节约PD',\n          current: '6.8 PD/月',\n          target: '>6 PD/月',\n          completionRate: 113,\n          status: 'success',\n          dataSource: '代码分析工具'\n        },\n        {\n          name: '维护提效PD',\n          current: '1.8 PD/月',\n          target: '>2 PD/月',\n          completionRate: 90,\n          status: 'warning',\n          dataSource: '版本管理系统'\n        }\n      ],\n      \n      // 成本维度一级指标\n      costL1Metrics: [\n        {\n          name: 'CDN节约',\n          currentSaving: 45000,\n          target: '>10%',\n          optimizationRate: 12.5,\n          progress: 125,\n          status: 'success',\n          dataSource: 'CDN服务商账单'\n        },\n        {\n          name: '计算资源节约',\n          currentSaving: 85000,\n          target: '>15%',\n          optimizationRate: 18.2,\n          progress: 121,\n          status: 'success',\n          dataSource: '云服务商账单'\n        },\n        {\n          name: '存储节约',\n          currentSaving: 28000,\n          target: '>8%',\n          optimizationRate: 9.8,\n          progress: 122,\n          status: 'success',\n          dataSource: '云服务商账单'\n        },\n        {\n          name: '带宽节约',\n          currentSaving: 27000,\n          target: '>12%',\n          optimizationRate: 14.5,\n          progress: 120,\n          status: 'success',\n          dataSource: '云服务商账单'\n        }\n      ],\n      \n      // 成本维度二级指标\n      costL2Metrics: [\n        {\n          name: '缓存节约',\n          currentSaving: 6200,\n          target: '>5,000元/月',\n          completionRate: 124,\n          status: 'success',\n          dataSource: 'CDN服务商',\n          trend: 'up',\n          trendText: '↗ +8.5%'\n        },\n        {\n          name: '压缩节约',\n          currentSaving: 3800,\n          target: '>3,000元/月',\n          completionRate: 126,\n          status: 'success',\n          dataSource: 'CDN服务商',\n          trend: 'up',\n          trendText: '↗ +12.3%'\n        },\n        {\n          name: '分发节约',\n          currentSaving: 2100,\n          target: '>2,000元/月',\n          completionRate: 105,\n          status: 'success',\n          dataSource: 'CDN服务商',\n          trend: 'stable',\n          trendText: '→ +2.1%'\n        },\n        {\n          name: 'CPU节约',\n          currentSaving: 9200,\n          target: '>8,000元/月',\n          completionRate: 115,\n          status: 'success',\n          dataSource: '云服务商',\n          trend: 'up',\n          trendText: '↗ +15.2%'\n        },\n        {\n          name: '内存节约',\n          currentSaving: 6800,\n          target: '>6,000元/月',\n          completionRate: 113,\n          status: 'success',\n          dataSource: '云服务商',\n          trend: 'up',\n          trendText: '↗ +9.8%'\n        },\n        {\n          name: '实例节约',\n          currentSaving: 3800,\n          target: '>4,000元/月',\n          completionRate: 95,\n          status: 'warning',\n          dataSource: '云服务商',\n          trend: 'down',\n          trendText: '↘ -2.5%'\n        }\n      ],\n      \n      // 重点执行指标\n      keyMetrics: [\n        {\n          name: '增量编译节约',\n          current: '68%',\n          target: '>60%',\n          dataSource: '构建系统',\n          status: 'success',\n          statusText: '达标'\n        },\n        {\n          name: '告警准确率',\n          current: '92%',\n          target: '>90%',\n          dataSource: '监控系统',\n          status: 'success',\n          statusText: '达标'\n        },\n        {\n          name: '组件使用频次',\n          current: '58次/组件',\n          target: '>50次/组件',\n          dataSource: '代码分析工具',\n          status: 'success',\n          statusText: '达标'\n        }\n      ],\n      \n      // 数据源状态\n      dataSources: [\n        {\n          name: '构建系统',\n          status: 'online',\n          lastUpdate: '2024-06-20 15:30:00'\n        },\n        {\n          name: '部署平台',\n          status: 'online',\n          lastUpdate: '2024-06-20 15:28:00'\n        },\n        {\n          name: '监控系统',\n          status: 'online',\n          lastUpdate: '2024-06-20 15:32:00'\n        },\n        {\n          name: '工单系统',\n          status: 'warning',\n          lastUpdate: '2024-06-20 14:45:00'\n        },\n        {\n          name: 'CDN服务商',\n          status: 'online',\n          lastUpdate: '2024-06-20 15:25:00'\n        },\n        {\n          name: '云服务商',\n          status: 'online',\n          lastUpdate: '2024-06-20 15:20:00'\n        }\n      ]\n    }\n  }\n}\n</script>\n\n<style scoped>\n.dashboard {\n  padding: 20px;\n  max-width: 1400px;\n  margin: 0 auto;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n}\n\n.page-header {\n  text-align: center;\n  margin-bottom: 40px;\n}\n\n.page-header h1 {\n  color: #2c3e50;\n  margin-bottom: 10px;\n  font-size: 2.5em;\n}\n\n.subtitle {\n  color: #7f8c8d;\n  font-size: 1.1em;\n}\n\n/* 北极星指标样式 */\n.overview-section {\n  margin-bottom: 40px;\n}\n\n.overview-section h2 {\n  color: #2c3e50;\n  margin-bottom: 20px;\n  border-bottom: 2px solid #3498db;\n  padding-bottom: 10px;\n}\n\n.metric-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n}\n\n.metric-card {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.metric-card.efficiency {\n  border-left: 4px solid #e74c3c;\n}\n\n.metric-card.cost {\n  border-left: 4px solid #27ae60;\n}\n\n.metric-icon {\n  font-size: 3em;\n}\n\n.metric-content h3 {\n  margin: 0 0 10px 0;\n  color: #2c3e50;\n  font-size: 1.1em;\n}\n\n.metric-value {\n  font-size: 2.5em;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 5px;\n}\n\n.metric-trend {\n  font-size: 1.1em;\n  font-weight: 500;\n}\n\n.metric-trend.positive {\n  color: #27ae60;\n}\n\n/* 提效维度样式 */\n.efficiency-section {\n  margin-bottom: 40px;\n}\n\n.efficiency-section h2 {\n  color: #2c3e50;\n  margin-bottom: 20px;\n  border-bottom: 2px solid #e74c3c;\n  padding-bottom: 10px;\n}\n\n.level-one-metrics {\n  margin-bottom: 30px;\n}\n\n.level-one-metrics h3 {\n  color: #34495e;\n  margin-bottom: 15px;\n}\n\n.metrics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 20px;\n}\n\n.metric-item {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.metric-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.metric-name {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.metric-target {\n  color: #7f8c8d;\n  font-size: 0.9em;\n}\n\n.metric-progress {\n  margin-bottom: 10px;\n}\n\n.progress-bar {\n  width: 100%;\n  height: 8px;\n  background-color: #ecf0f1;\n  border-radius: 4px;\n  overflow: hidden;\n  margin-bottom: 5px;\n}\n\n.progress-fill {\n  height: 100%;\n  background-color: #e74c3c;\n  transition: width 0.3s ease;\n}\n\n.progress-text {\n  font-size: 0.9em;\n  color: #7f8c8d;\n}\n\n.metric-details {\n  font-size: 0.8em;\n  color: #95a5a6;\n}\n\n/* 二级指标表格样式 */\n.level-two-metrics h3 {\n  color: #34495e;\n  margin-bottom: 20px;\n}\n\n.metric-category {\n  margin-bottom: 30px;\n}\n\n.metric-category h4 {\n  color: #2c3e50;\n  margin-bottom: 15px;\n  padding-left: 10px;\n  border-left: 3px solid #e74c3c;\n}\n\n.metrics-table {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.metrics-table table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.metrics-table th {\n  background-color: #f8f9fa;\n  padding: 15px;\n  text-align: left;\n  font-weight: 600;\n  color: #2c3e50;\n  border-bottom: 1px solid #dee2e6;\n}\n\n.metrics-table td {\n  padding: 15px;\n  border-bottom: 1px solid #dee2e6;\n}\n\n.completion-rate {\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 0.9em;\n  font-weight: 500;\n}\n\n.completion-rate.success {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n.completion-rate.warning {\n  background-color: #fff3cd;\n  color: #856404;\n}\n\n/* 成本维度样式 */\n.cost-section {\n  margin-bottom: 40px;\n}\n\n.cost-section h2 {\n  color: #2c3e50;\n  margin-bottom: 20px;\n  border-bottom: 2px solid #27ae60;\n  padding-bottom: 10px;\n}\n\n.cost-overview h3 {\n  color: #34495e;\n  margin-bottom: 15px;\n}\n\n.cost-metrics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.cost-metric-card {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  border-left: 4px solid #27ae60;\n}\n\n.cost-metric-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.cost-metric-header h4 {\n  margin: 0;\n  color: #2c3e50;\n}\n\n.cost-target {\n  color: #7f8c8d;\n  font-size: 0.9em;\n}\n\n.cost-value {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.current-value {\n  font-size: 1.5em;\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.optimization-rate {\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-weight: 500;\n}\n\n.optimization-rate.success {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n.cost-progress .progress-fill {\n  background-color: #27ae60;\n}\n\n.cost-source {\n  font-size: 0.8em;\n  color: #95a5a6;\n  margin-top: 10px;\n}\n\n/* 成本详细表格 */\n.cost-detail {\n  margin-top: 30px;\n}\n\n.cost-detail h3 {\n  color: #34495e;\n  margin-bottom: 15px;\n}\n\n.cost-detail-table {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.trend {\n  font-weight: 500;\n}\n\n.trend.up {\n  color: #27ae60;\n}\n\n.trend.down {\n  color: #e74c3c;\n}\n\n.trend.stable {\n  color: #f39c12;\n}\n\n/* 重点执行指标样式 */\n.key-metrics-section {\n  margin-bottom: 40px;\n}\n\n.key-metrics-section h2 {\n  color: #2c3e50;\n  margin-bottom: 20px;\n  border-bottom: 2px solid #9b59b6;\n  padding-bottom: 10px;\n}\n\n.key-metrics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n}\n\n.key-metric-card {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  border-left: 4px solid #9b59b6;\n}\n\n.key-metric-icon {\n  font-size: 2em;\n}\n\n.key-metric-content {\n  flex: 1;\n}\n\n.key-metric-content h4 {\n  margin: 0 0 8px 0;\n  color: #2c3e50;\n}\n\n.key-metric-value {\n  font-size: 1.8em;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 5px;\n}\n\n.key-metric-target {\n  color: #7f8c8d;\n  font-size: 0.9em;\n  margin-bottom: 5px;\n}\n\n.key-metric-source {\n  color: #95a5a6;\n  font-size: 0.8em;\n}\n\n.key-metric-status {\n  padding: 6px 12px;\n  border-radius: 4px;\n  font-weight: 500;\n  font-size: 0.9em;\n}\n\n.key-metric-status.success {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n/* 数据采集状态样式 */\n.data-collection-section h2 {\n  color: #2c3e50;\n  margin-bottom: 20px;\n  border-bottom: 2px solid #34495e;\n  padding-bottom: 10px;\n}\n\n.collection-status {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.status-item {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 10px 0;\n  border-bottom: 1px solid #ecf0f1;\n}\n\n.status-item:last-child {\n  border-bottom: none;\n}\n\n.status-indicator {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n}\n\n.status-indicator.online {\n  background-color: #27ae60;\n}\n\n.status-indicator.warning {\n  background-color: #f39c12;\n}\n\n.status-indicator.offline {\n  background-color: #e74c3c;\n}\n\n.source-name {\n  font-weight: 500;\n  color: #2c3e50;\n  min-width: 120px;\n}\n\n.last-update {\n  color: #7f8c8d;\n  font-size: 0.9em;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .dashboard {\n    padding: 15px;\n  }\n  \n  .metric-cards {\n    grid-template-columns: 1fr;\n  }\n  \n  .metrics-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .cost-metrics-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .key-metrics-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .metrics-table {\n    overflow-x: auto;\n  }\n  \n  .page-header h1 {\n    font-size: 2em;\n  }\n}\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAW;;EAQfA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAwB;;EAE5BA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAuB;;EAGjCA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAuB;;EAOrCA,KAAK,EAAC;AAAoB;;EAIxBA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAc;;EAEhBA,KAAK,EAAC;AAAe;;EAClBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAe;;EAExBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAc;;EAGnBA,KAAK,EAAC;AAAe;;EAExBA,KAAK,EAAC;AAAgB;;EACnBA,KAAK,EAAC;AAAa;;EAO5BA,KAAK,EAAC;AAAmB;;EAIvBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAe;;EA6BvBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAe;;EA6BvBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAe;;EA+B3BA,KAAK,EAAC;AAAc;;EAIlBA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAmB;;EAErBA,KAAK,EAAC;AAAoB;;EAEvBA,KAAK,EAAC;AAAa;;EAEtBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAY;;EACfA,KAAK,EAAC;AAAe;;EAKxBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;EAItBA,KAAK,EAAC;AAAa;;EAO3BA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAmB;;EAoC7BA,KAAK,EAAC;AAAqB;;EAEzBA,KAAK,EAAC;AAAkB;;EAGpBA,KAAK,EAAC;AAAoB;;EAExBA,KAAK,EAAC;AAAkB;;EACxBA,KAAK,EAAC;AAAmB;;EACzBA,KAAK,EAAC;AAAmB;;EAUjCA,KAAK,EAAC;AAAyB;;EAE7BA,KAAK,EAAC;AAAmB;;EAGpBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;uBA1PjCC,mBAAA,CA8PM,OA9PNC,UA8PM,GA7PJC,mBAAA,UAAa,E,4BACbC,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAAsB,YAAlB,eAAa,GACjBA,mBAAA,CAAyC;IAAtCJ,KAAK,EAAC;EAAU,GAAC,mBAAiB,E,qBAGvCG,mBAAA,aAAgB,EAChBC,mBAAA,CAoBM,OApBNC,UAoBM,G,0BAnBJD,mBAAA,CAAc,YAAV,OAAK,qBACTA,mBAAA,CAiBM,OAjBNE,UAiBM,GAhBJF,mBAAA,CAOM,OAPNG,UAOM,G,0BANJH,mBAAA,CAAgC;IAA3BJ,KAAK,EAAC;EAAa,GAAC,GAAC,qBAC1BI,mBAAA,CAIM,OAJNI,UAIM,G,0BAHJJ,mBAAA,CAAgB,YAAZ,SAAO,qBACXA,mBAAA,CAAqD,OAArDK,UAAqD,EAAAC,gBAAA,CAAxBC,KAAA,CAAAC,YAAY,IAAG,KAAG,iBAC/CR,mBAAA,CAAmE,OAAnES,UAAmE,EAAhC,GAAC,GAAAH,gBAAA,CAAGC,KAAA,CAAAG,cAAc,IAAG,OAAK,gB,KAGjEV,mBAAA,CAOM,OAPNW,UAOM,G,0BANJX,mBAAA,CAAiC;IAA5BJ,KAAK,EAAC;EAAa,GAAC,IAAE,qBAC3BI,mBAAA,CAIM,OAJNY,UAIM,G,0BAHJZ,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAAsE,OAAtEa,WAAsE,EAA5C,GAAC,GAAAP,gBAAA,CAAGC,KAAA,CAAAO,cAAc,CAACC,cAAc,oBAC3Df,mBAAA,CAAoF,OAApFgB,WAAoF,EAAjD,IAAE,GAAAV,gBAAA,CAAGC,KAAA,CAAAU,gBAAgB,CAACF,cAAc,MAAK,IAAE,gB,SAMtFhB,mBAAA,YAAe,EACfC,mBAAA,CA0HM,OA1HNkB,WA0HM,G,4BAzHJlB,mBAAA,CAAe,YAAX,QAAM,qBAEVD,mBAAA,YAAe,EACfC,mBAAA,CAmBM,OAnBNmB,WAmBM,G,0BAlBJnB,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAgBM,OAhBNoB,WAgBM,I,kBAfJvB,mBAAA,CAcMwB,SAAA,QAAAC,WAAA,CAdoCf,KAAA,CAAAgB,mBAAmB,EAA7BC,MAAM;yBAAtC3B,mBAAA,CAcM;MAdDD,KAAK,EAAC,aAAa;MAAwC6B,GAAG,EAAED,MAAM,CAACE;QAC1E1B,mBAAA,CAGM,OAHN2B,WAGM,GAFJ3B,mBAAA,CAAkD,QAAlD4B,WAAkD,EAAAtB,gBAAA,CAArBkB,MAAM,CAACE,IAAI,kBACxC1B,mBAAA,CAA0D,QAA1D6B,WAA0D,EAA9B,MAAI,GAAAvB,gBAAA,CAAGkB,MAAM,CAACM,MAAM,iB,GAElD9B,mBAAA,CAKM,OALN+B,WAKM,GAJJ/B,mBAAA,CAEM,OAFNgC,WAEM,GADJhC,mBAAA,CAAyE;MAApEJ,KAAK,EAAC,eAAe;MAAEqC,KAAK,EAAAC,eAAA;QAAAC,KAAA,EAAUX,MAAM,CAACY,QAAQ;MAAA;+BAE5DpC,mBAAA,CAA6E,QAA7EqC,WAA6E,EAAA/B,gBAAA,CAA9CkB,MAAM,CAACc,OAAO,IAAG,KAAG,GAAAhC,gBAAA,CAAGkB,MAAM,CAACM,MAAM,iB,GAErE9B,mBAAA,CAEM,OAFNuC,WAEM,GADJvC,mBAAA,CAA8D,QAA9DwC,WAA8D,EAApC,QAAM,GAAAlC,gBAAA,CAAGkB,MAAM,CAACiB,UAAU,iB;sCAM5D1C,mBAAA,YAAe,EACfC,mBAAA,CA+FM,OA/FN0C,WA+FM,G,4BA9FJ1C,mBAAA,CAAe,YAAX,QAAM,qBAEVD,mBAAA,WAAc,EACdC,mBAAA,CA4BM,OA5BN2C,WA4BM,G,0BA3BJ3C,mBAAA,CAAc,YAAV,OAAK,qBACTA,mBAAA,CAyBM,OAzBN4C,WAyBM,GAxBJ5C,mBAAA,CAuBQ,gB,0BAtBNA,mBAAA,CAQQ,gBAPNA,mBAAA,CAMK,aALHA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAa,YAAT,MAAI,E,uBAGZA,mBAAA,CAYQ,iB,kBAXNH,mBAAA,CAUKwB,SAAA,QAAAC,WAAA,CAVgBf,KAAA,CAAAsC,YAAY,EAAtBrB,MAAM;yBAAjB3B,mBAAA,CAUK;MAV+B4B,GAAG,EAAED,MAAM,CAACE;QAC9C1B,mBAAA,CAA0B,YAAAM,gBAAA,CAAnBkB,MAAM,CAACE,IAAI,kBAClB1B,mBAAA,CAA6B,YAAAM,gBAAA,CAAtBkB,MAAM,CAACc,OAAO,kBACrBtC,mBAAA,CAA4B,YAAAM,gBAAA,CAArBkB,MAAM,CAACM,MAAM,kBACpB9B,mBAAA,CAIK,aAHHA,mBAAA,CAEO;MAFAJ,KAAK,EAAAkD,eAAA,qBAAsBtB,MAAM,CAACuB,MAAM;wBAC1CvB,MAAM,CAACwB,cAAc,IAAG,IAC7B,uB,GAEFhD,mBAAA,CAAgC,YAAAM,gBAAA,CAAzBkB,MAAM,CAACiB,UAAU,iB;0CAOlC1C,mBAAA,aAAgB,EAChBC,mBAAA,CA4BM,OA5BNiD,WA4BM,G,0BA3BJjD,mBAAA,CAAgB,YAAZ,SAAO,qBACXA,mBAAA,CAyBM,OAzBNkD,WAyBM,GAxBJlD,mBAAA,CAuBQ,gB,0BAtBNA,mBAAA,CAQQ,gBAPNA,mBAAA,CAMK,aALHA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAa,YAAT,MAAI,E,uBAGZA,mBAAA,CAYQ,iB,kBAXNH,mBAAA,CAUKwB,SAAA,QAAAC,WAAA,CAVgBf,KAAA,CAAA4C,cAAc,EAAxB3B,MAAM;yBAAjB3B,mBAAA,CAUK;MAViC4B,GAAG,EAAED,MAAM,CAACE;QAChD1B,mBAAA,CAA0B,YAAAM,gBAAA,CAAnBkB,MAAM,CAACE,IAAI,kBAClB1B,mBAAA,CAA6B,YAAAM,gBAAA,CAAtBkB,MAAM,CAACc,OAAO,kBACrBtC,mBAAA,CAA4B,YAAAM,gBAAA,CAArBkB,MAAM,CAACM,MAAM,kBACpB9B,mBAAA,CAIK,aAHHA,mBAAA,CAEO;MAFAJ,KAAK,EAAAkD,eAAA,qBAAsBtB,MAAM,CAACuB,MAAM;wBAC1CvB,MAAM,CAACwB,cAAc,IAAG,IAC7B,uB,GAEFhD,mBAAA,CAAgC,YAAAM,gBAAA,CAAzBkB,MAAM,CAACiB,UAAU,iB;0CAOlC1C,mBAAA,WAAc,EACdC,mBAAA,CA4BM,OA5BNoD,WA4BM,G,4BA3BJpD,mBAAA,CAAc,YAAV,OAAK,qBACTA,mBAAA,CAyBM,OAzBNqD,WAyBM,GAxBJrD,mBAAA,CAuBQ,gB,4BAtBNA,mBAAA,CAQQ,gBAPNA,mBAAA,CAMK,aALHA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAa,YAAT,MAAI,E,uBAGZA,mBAAA,CAYQ,iB,kBAXNH,mBAAA,CAUKwB,SAAA,QAAAC,WAAA,CAVgBf,KAAA,CAAA+C,gBAAgB,EAA1B9B,MAAM;yBAAjB3B,mBAAA,CAUK;MAVmC4B,GAAG,EAAED,MAAM,CAACE;QAClD1B,mBAAA,CAA0B,YAAAM,gBAAA,CAAnBkB,MAAM,CAACE,IAAI,kBAClB1B,mBAAA,CAA6B,YAAAM,gBAAA,CAAtBkB,MAAM,CAACc,OAAO,kBACrBtC,mBAAA,CAA4B,YAAAM,gBAAA,CAArBkB,MAAM,CAACM,MAAM,kBACpB9B,mBAAA,CAIK,aAHHA,mBAAA,CAEO;MAFAJ,KAAK,EAAAkD,eAAA,qBAAsBtB,MAAM,CAACuB,MAAM;wBAC1CvB,MAAM,CAACwB,cAAc,IAAG,IAC7B,uB,GAEFhD,mBAAA,CAAgC,YAAAM,gBAAA,CAAzBkB,MAAM,CAACiB,UAAU,iB;8CAStC1C,mBAAA,YAAe,EACfC,mBAAA,CAkEM,OAlENuD,WAkEM,G,4BAjEJvD,mBAAA,CAAe,YAAX,QAAM,qBAEVD,mBAAA,YAAe,EACfC,mBAAA,CAwBM,OAxBNwD,WAwBM,G,4BAvBJxD,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAqBM,OArBNyD,WAqBM,I,kBApBJ5D,mBAAA,CAmBMwB,SAAA,QAAAC,WAAA,CAnByCf,KAAA,CAAAmD,aAAa,EAAvBlC,MAAM;yBAA3C3B,mBAAA,CAmBM;MAnBDD,KAAK,EAAC,kBAAkB;MAAkC6B,GAAG,EAAED,MAAM,CAACE;QACzE1B,mBAAA,CAGM,OAHN2D,WAGM,GAFJ3D,mBAAA,CAA0B,YAAAM,gBAAA,CAAnBkB,MAAM,CAACE,IAAI,kBAClB1B,mBAAA,CAAwD,QAAxD4D,WAAwD,EAA9B,MAAI,GAAAtD,gBAAA,CAAGkB,MAAM,CAACM,MAAM,iB,GAEhD9B,mBAAA,CAaM,OAbN6D,WAaM,GAZJ7D,mBAAA,CAKM,OALN8D,WAKM,GAJJ9D,mBAAA,CAA+E,QAA/E+D,WAA+E,EAAnD,GAAC,GAAAzD,gBAAA,CAAGkB,MAAM,CAACwC,aAAa,CAACjD,cAAc,oBACnEf,mBAAA,CAEO;MAFDJ,KAAK,EAAAkD,eAAA,EAAC,mBAAmB,EAAStB,MAAM,CAACuB,MAAM;wBAChDvB,MAAM,CAACyC,gBAAgB,IAAG,IAC/B,uB,GAEFjE,mBAAA,CAIM,OAJNkE,WAIM,GAHJlE,mBAAA,CAEM,OAFNmE,WAEM,GADJnE,mBAAA,CAAyE;MAApEJ,KAAK,EAAC,eAAe;MAAEqC,KAAK,EAAAC,eAAA;QAAAC,KAAA,EAAUX,MAAM,CAACY,QAAQ;MAAA;iCAG9DpC,mBAAA,CAAsD,OAAtDoE,WAAsD,EAAA9D,gBAAA,CAA1BkB,MAAM,CAACiB,UAAU,iB;sCAMrD1C,mBAAA,YAAe,EACfC,mBAAA,CAkCM,OAlCNqE,WAkCM,G,4BAjCJrE,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CA+BM,OA/BNsE,WA+BM,GA9BJtE,mBAAA,CA6BQ,gB,4BA5BNA,mBAAA,CASQ,gBARNA,mBAAA,CAOK,aANHA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAe,YAAX,QAAM,GACVA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAY,YAAR,KAAG,GACPA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAW,YAAP,IAAE,E,uBAGVA,mBAAA,CAiBQ,iB,kBAhBNH,mBAAA,CAeKwB,SAAA,QAAAC,WAAA,CAfgBf,KAAA,CAAAgE,aAAa,EAAvB/C,MAAM;yBAAjB3B,mBAAA,CAeK;MAfgC4B,GAAG,EAAED,MAAM,CAACE;QAC/C1B,mBAAA,CAA0B,YAAAM,gBAAA,CAAnBkB,MAAM,CAACE,IAAI,kBAClB1B,mBAAA,CAAqD,YAAjD,GAAC,GAAAM,gBAAA,CAAGkB,MAAM,CAACwC,aAAa,CAACjD,cAAc,oBAC3Cf,mBAAA,CAA4B,YAAAM,gBAAA,CAArBkB,MAAM,CAACM,MAAM,kBACpB9B,mBAAA,CAIK,aAHHA,mBAAA,CAEO;MAFAJ,KAAK,EAAAkD,eAAA,qBAAsBtB,MAAM,CAACuB,MAAM;wBAC1CvB,MAAM,CAACwB,cAAc,IAAG,IAC7B,uB,GAEFhD,mBAAA,CAAgC,YAAAM,gBAAA,CAAzBkB,MAAM,CAACiB,UAAU,kBACxBzC,mBAAA,CAIK,aAHHA,mBAAA,CAEO;MAFAJ,KAAK,EAAAkD,eAAA,WAAYtB,MAAM,CAACgD,KAAK;wBAC/BhD,MAAM,CAACiD,SAAS,wB;4CAUnC1E,mBAAA,YAAe,EACfC,mBAAA,CAgBM,OAhBN0E,WAgBM,G,4BAfJ1E,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAaM,OAbN2E,WAaM,I,kBAZJ9E,mBAAA,CAWMwB,SAAA,QAAAC,WAAA,CAXwCf,KAAA,CAAAqE,UAAU,EAApBpD,MAAM;yBAA1C3B,mBAAA,CAWM;MAXDD,KAAK,EAAC,iBAAiB;MAA+B6B,GAAG,EAAED,MAAM,CAACE;oCACrE1B,mBAAA,CAAqC;MAAhCJ,KAAK,EAAC;IAAiB,GAAC,IAAE,qBAC/BI,mBAAA,CAKM,OALN6E,WAKM,GAJJ7E,mBAAA,CAA0B,YAAAM,gBAAA,CAAnBkB,MAAM,CAACE,IAAI,kBAClB1B,mBAAA,CAAwD,OAAxD8E,WAAwD,EAAAxE,gBAAA,CAAvBkB,MAAM,CAACc,OAAO,kBAC/CtC,mBAAA,CAA4D,OAA5D+E,WAA4D,EAA7B,MAAI,GAAAzE,gBAAA,CAAGkB,MAAM,CAACM,MAAM,kBACnD9B,mBAAA,CAA4D,OAA5DgF,WAA4D,EAAA1E,gBAAA,CAA1BkB,MAAM,CAACiB,UAAU,iB,GAErDzC,mBAAA,CAEM;MAFDJ,KAAK,EAAAkD,eAAA,EAAC,mBAAmB,EAAStB,MAAM,CAACuB,MAAM;wBAC/CvB,MAAM,CAACyD,UAAU,wB;sCAM5BlF,mBAAA,YAAe,EACfC,mBAAA,CASM,OATNkF,WASM,G,4BARJlF,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAMM,OANNmF,WAMM,I,kBALJtF,mBAAA,CAIMwB,SAAA,QAAAC,WAAA,CAJoCf,KAAA,CAAA6E,WAAW,EAArBC,MAAM;yBAAtCxF,mBAAA,CAIM;MAJDD,KAAK,EAAC,aAAa;MAAgC6B,GAAG,EAAE4D,MAAM,CAAC3D;QAClE1B,mBAAA,CAA2D;MAAtDJ,KAAK,EAAAkD,eAAA,EAAC,kBAAkB,EAASuC,MAAM,CAACtC,MAAM;6BACnD/C,mBAAA,CAAkD,QAAlDsF,WAAkD,EAAAhF,gBAAA,CAArB+E,MAAM,CAAC3D,IAAI,kBACxC1B,mBAAA,CAA8D,QAA9DuF,WAA8D,EAApC,QAAM,GAAAjF,gBAAA,CAAG+E,MAAM,CAACG,UAAU,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}