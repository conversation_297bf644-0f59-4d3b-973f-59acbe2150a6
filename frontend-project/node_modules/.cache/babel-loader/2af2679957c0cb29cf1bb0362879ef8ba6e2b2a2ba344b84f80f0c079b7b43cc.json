{"ast": null, "code": "import { createApp } from 'vue';\nimport App from './App.vue';\ncreateApp(App).mount('#app');", "map": {"version": 3, "names": ["createApp", "App", "mount"], "sources": ["/Users/<USER>/Desktop/dev/frontend-project/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\nimport App from './App.vue'\n\ncreateApp(App).mount('#app')\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAE3BD,SAAS,CAACC,GAAG,CAAC,CAACC,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}