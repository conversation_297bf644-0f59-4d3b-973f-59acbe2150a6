{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/**\n * Translates the list format produced by css-loader into something\n * easier to manipulate.\n */\nexport default function listToStyles(parentId, list) {\n  var styles = [];\n  var newStyles = {};\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i];\n    var id = item[0];\n    var css = item[1];\n    var media = item[2];\n    var sourceMap = item[3];\n    var part = {\n      id: parentId + ':' + i,\n      css: css,\n      media: media,\n      sourceMap: sourceMap\n    };\n    if (!newStyles[id]) {\n      styles.push(newStyles[id] = {\n        id: id,\n        parts: [part]\n      });\n    } else {\n      newStyles[id].parts.push(part);\n    }\n  }\n  return styles;\n}", "map": {"version": 3, "names": ["listToStyles", "parentId", "list", "styles", "newStyles", "i", "length", "item", "id", "css", "media", "sourceMap", "part", "push", "parts"], "sources": ["/Users/<USER>/Desktop/dev/frontend-project/node_modules/.pnpm/vue-style-loader@4.1.3/node_modules/vue-style-loader/lib/listToStyles.js"], "sourcesContent": ["/**\n * Translates the list format produced by css-loader into something\n * easier to manipulate.\n */\nexport default function listToStyles (parentId, list) {\n  var styles = []\n  var newStyles = {}\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i]\n    var id = item[0]\n    var css = item[1]\n    var media = item[2]\n    var sourceMap = item[3]\n    var part = {\n      id: parentId + ':' + i,\n      css: css,\n      media: media,\n      sourceMap: sourceMap\n    }\n    if (!newStyles[id]) {\n      styles.push(newStyles[id] = { id: id, parts: [part] })\n    } else {\n      newStyles[id].parts.push(part)\n    }\n  }\n  return styles\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,eAAe,SAASA,YAAYA,CAAEC,QAAQ,EAAEC,IAAI,EAAE;EACpD,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,SAAS,GAAG,CAAC,CAAC;EAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,IAAIE,IAAI,GAAGL,IAAI,CAACG,CAAC,CAAC;IAClB,IAAIG,EAAE,GAAGD,IAAI,CAAC,CAAC,CAAC;IAChB,IAAIE,GAAG,GAAGF,IAAI,CAAC,CAAC,CAAC;IACjB,IAAIG,KAAK,GAAGH,IAAI,CAAC,CAAC,CAAC;IACnB,IAAII,SAAS,GAAGJ,IAAI,CAAC,CAAC,CAAC;IACvB,IAAIK,IAAI,GAAG;MACTJ,EAAE,EAAEP,QAAQ,GAAG,GAAG,GAAGI,CAAC;MACtBI,GAAG,EAAEA,GAAG;MACRC,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAEA;IACb,CAAC;IACD,IAAI,CAACP,SAAS,CAACI,EAAE,CAAC,EAAE;MAClBL,MAAM,CAACU,IAAI,CAACT,SAAS,CAACI,EAAE,CAAC,GAAG;QAAEA,EAAE,EAAEA,EAAE;QAAEM,KAAK,EAAE,CAACF,IAAI;MAAE,CAAC,CAAC;IACxD,CAAC,MAAM;MACLR,SAAS,CAACI,EAAE,CAAC,CAACM,KAAK,CAACD,IAAI,CAACD,IAAI,CAAC;IAChC;EACF;EACA,OAAOT,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}