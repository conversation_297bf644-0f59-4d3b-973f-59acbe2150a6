{"ast": null, "code": "export default {\n  name: 'DashboardView',\n  data() {\n    return {\n      // 筛选器相关数据\n      selectedOrganization: 'all',\n      selectedTimeRange: '30d',\n      customStartDate: '',\n      customEndDate: '',\n      // 组织列表\n      organizations: [{\n        id: 'frontend',\n        name: '前端团队'\n      }, {\n        id: 'backend',\n        name: '后端团队'\n      }, {\n        id: 'mobile',\n        name: '移动端团队'\n      }, {\n        id: 'devops',\n        name: 'DevOps团队'\n      }, {\n        id: 'qa',\n        name: '测试团队'\n      }, {\n        id: 'design',\n        name: '设计团队'\n      }],\n      // 北极星指标\n      totalPDSaved: 156.5,\n      monthlyPDSaved: 28.3,\n      totalCostSaved: 1250000,\n      monthlyCostSaved: 185000,\n      // 提效维度一级指标\n      efficiencyL1Metrics: [{\n        name: '构建提效PD',\n        current: '22.5 PD/月',\n        target: '>20 PD/月',\n        progress: 112,\n        dataSource: '构建系统、部署平台'\n      }, {\n        name: '问题提效PD',\n        current: '18.2 PD/月',\n        target: '>15 PD/月',\n        progress: 121,\n        dataSource: '监控系统、工单系统'\n      }, {\n        name: '组件提效PD',\n        current: '12.8 PD/月',\n        target: '>10 PD/月',\n        progress: 128,\n        dataSource: '代码仓库、组件库'\n      }],\n      // 工程构建类指标\n      buildMetrics: [{\n        name: '构建速度PD',\n        current: '9.2 PD/月',\n        target: '>8 PD/月',\n        completionRate: 115,\n        status: 'success',\n        dataSource: '构建系统日志'\n      }, {\n        name: '部署效率PD',\n        current: '7.8 PD/月',\n        target: '>6 PD/月',\n        completionRate: 130,\n        status: 'success',\n        dataSource: '部署平台日志'\n      }, {\n        name: '开发体验PD',\n        current: '5.5 PD/月',\n        target: '>4 PD/月',\n        completionRate: 137,\n        status: 'success',\n        dataSource: 'IDE插件统计'\n      }],\n      // 线上问题处理类指标\n      problemMetrics: [{\n        name: '发现提效PD',\n        current: '6.2 PD/月',\n        target: '>5 PD/月',\n        completionRate: 124,\n        status: 'success',\n        dataSource: '监控系统'\n      }, {\n        name: '定位提效PD',\n        current: '9.5 PD/月',\n        target: '>8 PD/月',\n        completionRate: 118,\n        status: 'success',\n        dataSource: '工单系统'\n      }, {\n        name: '解决提效PD',\n        current: '2.5 PD/月',\n        target: '>3 PD/月',\n        completionRate: 83,\n        status: 'warning',\n        dataSource: '工单系统'\n      }],\n      // 组件复用类指标\n      componentMetrics: [{\n        name: '开发提效PD',\n        current: '4.2 PD/月',\n        target: '>3 PD/月',\n        completionRate: 140,\n        status: 'success',\n        dataSource: '项目管理工具'\n      }, {\n        name: '复用节约PD',\n        current: '6.8 PD/月',\n        target: '>6 PD/月',\n        completionRate: 113,\n        status: 'success',\n        dataSource: '代码分析工具'\n      }, {\n        name: '维护提效PD',\n        current: '1.8 PD/月',\n        target: '>2 PD/月',\n        completionRate: 90,\n        status: 'warning',\n        dataSource: '版本管理系统'\n      }],\n      // 成本维度一级指标\n      costL1Metrics: [{\n        name: 'CDN节约',\n        currentSaving: 45000,\n        target: '>10%',\n        optimizationRate: 12.5,\n        progress: 125,\n        status: 'success',\n        dataSource: 'CDN服务商账单'\n      }, {\n        name: '计算资源节约',\n        currentSaving: 85000,\n        target: '>15%',\n        optimizationRate: 18.2,\n        progress: 121,\n        status: 'success',\n        dataSource: '云服务商账单'\n      }, {\n        name: '存储节约',\n        currentSaving: 28000,\n        target: '>8%',\n        optimizationRate: 9.8,\n        progress: 122,\n        status: 'success',\n        dataSource: '云服务商账单'\n      }, {\n        name: '带宽节约',\n        currentSaving: 27000,\n        target: '>12%',\n        optimizationRate: 14.5,\n        progress: 120,\n        status: 'success',\n        dataSource: '云服务商账单'\n      }],\n      // 成本维度二级指标\n      costL2Metrics: [{\n        name: '缓存节约',\n        currentSaving: 6200,\n        target: '>5,000元/月',\n        completionRate: 124,\n        status: 'success',\n        dataSource: 'CDN服务商',\n        trend: 'up',\n        trendText: '↗ +8.5%'\n      }, {\n        name: '压缩节约',\n        currentSaving: 3800,\n        target: '>3,000元/月',\n        completionRate: 126,\n        status: 'success',\n        dataSource: 'CDN服务商',\n        trend: 'up',\n        trendText: '↗ +12.3%'\n      }, {\n        name: '分发节约',\n        currentSaving: 2100,\n        target: '>2,000元/月',\n        completionRate: 105,\n        status: 'success',\n        dataSource: 'CDN服务商',\n        trend: 'stable',\n        trendText: '→ +2.1%'\n      }, {\n        name: 'CPU节约',\n        currentSaving: 9200,\n        target: '>8,000元/月',\n        completionRate: 115,\n        status: 'success',\n        dataSource: '云服务商',\n        trend: 'up',\n        trendText: '↗ +15.2%'\n      }, {\n        name: '内存节约',\n        currentSaving: 6800,\n        target: '>6,000元/月',\n        completionRate: 113,\n        status: 'success',\n        dataSource: '云服务商',\n        trend: 'up',\n        trendText: '↗ +9.8%'\n      }, {\n        name: '实例节约',\n        currentSaving: 3800,\n        target: '>4,000元/月',\n        completionRate: 95,\n        status: 'warning',\n        dataSource: '云服务商',\n        trend: 'down',\n        trendText: '↘ -2.5%'\n      }],\n      // 重点执行指标\n      keyMetrics: [{\n        name: '增量编译节约',\n        current: '68%',\n        target: '>60%',\n        dataSource: '构建系统',\n        status: 'success',\n        statusText: '达标'\n      }, {\n        name: '告警准确率',\n        current: '92%',\n        target: '>90%',\n        dataSource: '监控系统',\n        status: 'success',\n        statusText: '达标'\n      }, {\n        name: '组件使用频次',\n        current: '58次/组件',\n        target: '>50次/组件',\n        dataSource: '代码分析工具',\n        status: 'success',\n        statusText: '达标'\n      }],\n      // 数据源状态\n      dataSources: [{\n        name: '构建系统',\n        status: 'online',\n        lastUpdate: '2024-06-20 15:30:00'\n      }, {\n        name: '部署平台',\n        status: 'online',\n        lastUpdate: '2024-06-20 15:28:00'\n      }, {\n        name: '监控系统',\n        status: 'online',\n        lastUpdate: '2024-06-20 15:32:00'\n      }, {\n        name: '工单系统',\n        status: 'warning',\n        lastUpdate: '2024-06-20 14:45:00'\n      }, {\n        name: 'CDN服务商',\n        status: 'online',\n        lastUpdate: '2024-06-20 15:25:00'\n      }, {\n        name: '云服务商',\n        status: 'online',\n        lastUpdate: '2024-06-20 15:20:00'\n      }]\n    };\n  },\n  methods: {\n    // 组织变更处理\n    onOrganizationChange() {\n      console.log('组织切换到:', this.selectedOrganization);\n      this.loadDataByFilters();\n    },\n    // 时间范围变更处理\n    onTimeRangeChange() {\n      console.log('时间范围切换到:', this.selectedTimeRange);\n      if (this.selectedTimeRange !== 'custom') {\n        this.loadDataByFilters();\n      }\n    },\n    // 自定义日期变更处理\n    onCustomDateChange() {\n      if (this.customStartDate && this.customEndDate) {\n        console.log('自定义时间范围:', this.customStartDate, '到', this.customEndDate);\n        this.loadDataByFilters();\n      }\n    },\n    // 应用筛选\n    applyFilters() {\n      console.log('应用筛选条件:', {\n        organization: this.selectedOrganization,\n        timeRange: this.selectedTimeRange,\n        customStartDate: this.customStartDate,\n        customEndDate: this.customEndDate\n      });\n      this.loadDataByFilters();\n    },\n    // 重置筛选\n    resetFilters() {\n      this.selectedOrganization = 'all';\n      this.selectedTimeRange = '30d';\n      this.customStartDate = '';\n      this.customEndDate = '';\n      console.log('筛选条件已重置');\n      this.loadDataByFilters();\n    },\n    // 根据筛选条件加载数据\n    loadDataByFilters() {\n      // 这里可以根据筛选条件调用API获取数据\n      // 目前只是模拟数据更新\n      const orgMultiplier = this.getOrgMultiplier();\n      const timeMultiplier = this.getTimeMultiplier();\n\n      // 更新北极星指标\n      this.totalPDSaved = (156.5 * orgMultiplier * timeMultiplier).toFixed(1);\n      this.monthlyPDSaved = (28.3 * orgMultiplier).toFixed(1);\n      this.totalCostSaved = Math.round(1250000 * orgMultiplier * timeMultiplier);\n      this.monthlyCostSaved = Math.round(185000 * orgMultiplier);\n      console.log('数据已根据筛选条件更新');\n    },\n    // 获取组织系数\n    getOrgMultiplier() {\n      const multipliers = {\n        'all': 1.0,\n        'frontend': 0.4,\n        'backend': 0.3,\n        'mobile': 0.15,\n        'devops': 0.1,\n        'qa': 0.03,\n        'design': 0.02\n      };\n      return multipliers[this.selectedOrganization] || 1.0;\n    },\n    // 获取时间系数\n    getTimeMultiplier() {\n      const multipliers = {\n        '7d': 0.25,\n        '30d': 1.0,\n        '90d': 3.0,\n        '6m': 6.0,\n        '1y': 12.0,\n        'custom': 1.0\n      };\n      return multipliers[this.selectedTimeRange] || 1.0;\n    },\n    // 格式化日期显示\n    formatDate(date) {\n      return new Date(date).toLocaleDateString('zh-CN');\n    }\n  },\n  mounted() {\n    // 组件挂载时设置默认的自定义日期\n    const today = new Date();\n    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);\n    this.customEndDate = today.toISOString().split('T')[0];\n    this.customStartDate = thirtyDaysAgo.toISOString().split('T')[0];\n  }\n};", "map": {"version": 3, "names": ["name", "data", "selectedOrganization", "selectedTimeRange", "customStartDate", "customEndDate", "organizations", "id", "totalPDSaved", "monthlyPDSaved", "totalCostSaved", "monthlyCostSaved", "efficiencyL1Metrics", "current", "target", "progress", "dataSource", "buildMetrics", "completionRate", "status", "problemMetrics", "componentMetrics", "costL1Metrics", "currentSaving", "optimizationRate", "costL2Metrics", "trend", "trendText", "keyMetrics", "statusText", "dataSources", "lastUpdate", "methods", "onOrganizationChange", "console", "log", "loadDataByFilters", "onTimeRangeChange", "onCustomDateChange", "applyFilters", "organization", "timeRange", "resetFilters", "orgMultiplier", "getOrgMultiplier", "timeMultiplier", "getTimeMultiplier", "toFixed", "Math", "round", "multipliers", "formatDate", "date", "Date", "toLocaleDateString", "mounted", "today", "thirtyDaysAgo", "getTime", "toISOString", "split"], "sources": ["/Users/<USER>/Desktop/dev/frontend-project/src/views/Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard\">\n    <header class=\"dashboard-header\">\n      <div class=\"header-content\">\n        <h1>量化前端基建效率提升与成本节约</h1>\n\n        <!-- 筛选器区域 -->\n        <div class=\"filters-section\">\n          <div class=\"filter-group\">\n            <label for=\"organization\">组织:</label>\n            <select id=\"organization\" v-model=\"selectedOrganization\" @change=\"onOrganizationChange\">\n              <option value=\"all\">全部组织</option>\n              <option v-for=\"org in organizations\" :key=\"org.id\" :value=\"org.id\">\n                {{ org.name }}\n              </option>\n            </select>\n          </div>\n\n          <div class=\"filter-group\">\n            <label for=\"timeRange\">时间范围:</label>\n            <select id=\"timeRange\" v-model=\"selectedTimeRange\" @change=\"onTimeRangeChange\">\n              <option value=\"7d\">最近7天</option>\n              <option value=\"30d\">最近30天</option>\n              <option value=\"90d\">最近90天</option>\n              <option value=\"6m\">最近6个月</option>\n              <option value=\"1y\">最近1年</option>\n              <option value=\"custom\">自定义</option>\n            </select>\n          </div>\n\n          <!-- 自定义时间选择器 -->\n          <div v-if=\"selectedTimeRange === 'custom'\" class=\"custom-date-range\">\n            <div class=\"date-input-group\">\n              <label for=\"startDate\">开始日期:</label>\n              <input\n                type=\"date\"\n                id=\"startDate\"\n                v-model=\"customStartDate\"\n                @change=\"onCustomDateChange\"\n              >\n            </div>\n            <div class=\"date-input-group\">\n              <label for=\"endDate\">结束日期:</label>\n              <input\n                type=\"date\"\n                id=\"endDate\"\n                v-model=\"customEndDate\"\n                @change=\"onCustomDateChange\"\n              >\n            </div>\n          </div>\n\n          <!-- 应用筛选按钮 -->\n          <button class=\"apply-filters-btn\" @click=\"applyFilters\">\n            <span class=\"btn-icon\">🔍</span>\n            应用筛选\n          </button>\n\n          <!-- 重置筛选按钮 -->\n          <button class=\"reset-filters-btn\" @click=\"resetFilters\">\n            <span class=\"btn-icon\">🔄</span>\n            重置\n          </button>\n        </div>\n      </div>\n    </header>\n\n    <!-- 北极星指标概览 -->\n    <div class=\"overview-section\">\n      <h2>北极星指标</h2>\n      <div class=\"metric-cards\">\n        <div class=\"metric-card efficiency\">\n          <div class=\"metric-icon\">⚡</div>\n          <div class=\"metric-content\">\n            <h3>累计节约人天数</h3>\n            <div class=\"metric-value\">{{ totalPDSaved }} PD</div>\n            <div class=\"metric-trend positive\">+{{ monthlyPDSaved }} PD/月</div>\n          </div>\n        </div>\n        <div class=\"metric-card cost\">\n          <div class=\"metric-icon\">💰</div>\n          <div class=\"metric-content\">\n            <h3>累计成本节约</h3>\n            <div class=\"metric-value\">¥{{ totalCostSaved.toLocaleString() }}</div>\n            <div class=\"metric-trend positive\">+¥{{ monthlyCostSaved.toLocaleString() }}/月</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 提效维度统计 -->\n    <div class=\"efficiency-section\">\n      <h2>提效维度统计</h2>\n      \n      <!-- 一级过程指标 -->\n      <div class=\"level-one-metrics\">\n        <h3>一级过程指标</h3>\n        <div class=\"metrics-grid\">\n          <div class=\"metric-item\" v-for=\"metric in efficiencyL1Metrics\" :key=\"metric.name\">\n            <div class=\"metric-header\">\n              <span class=\"metric-name\">{{ metric.name }}</span>\n              <span class=\"metric-target\">目标: {{ metric.target }}</span>\n            </div>\n            <div class=\"metric-progress\">\n              <div class=\"progress-bar\">\n                <div class=\"progress-fill\" :style=\"{width: metric.progress + '%'}\"></div>\n              </div>\n              <span class=\"progress-text\">{{ metric.current }} / {{ metric.target }}</span>\n            </div>\n            <div class=\"metric-details\">\n              <span class=\"data-source\">数据来源: {{ metric.dataSource }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 二级过程指标 -->\n      <div class=\"level-two-metrics\">\n        <h3>二级过程指标</h3>\n        \n        <!-- 工程构建类 -->\n        <div class=\"metric-category\">\n          <h4>工程构建类</h4>\n          <div class=\"metrics-table\">\n            <table>\n              <thead>\n                <tr>\n                  <th>指标名称</th>\n                  <th>当前值</th>\n                  <th>目标值</th>\n                  <th>完成率</th>\n                  <th>数据来源</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr v-for=\"metric in buildMetrics\" :key=\"metric.name\">\n                  <td>{{ metric.name }}</td>\n                  <td>{{ metric.current }}</td>\n                  <td>{{ metric.target }}</td>\n                  <td>\n                    <span :class=\"['completion-rate', metric.status]\">\n                      {{ metric.completionRate }}%\n                    </span>\n                  </td>\n                  <td>{{ metric.dataSource }}</td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        <!-- 线上问题处理类 -->\n        <div class=\"metric-category\">\n          <h4>线上问题处理类</h4>\n          <div class=\"metrics-table\">\n            <table>\n              <thead>\n                <tr>\n                  <th>指标名称</th>\n                  <th>当前值</th>\n                  <th>目标值</th>\n                  <th>完成率</th>\n                  <th>数据来源</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr v-for=\"metric in problemMetrics\" :key=\"metric.name\">\n                  <td>{{ metric.name }}</td>\n                  <td>{{ metric.current }}</td>\n                  <td>{{ metric.target }}</td>\n                  <td>\n                    <span :class=\"['completion-rate', metric.status]\">\n                      {{ metric.completionRate }}%\n                    </span>\n                  </td>\n                  <td>{{ metric.dataSource }}</td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        <!-- 组件复用类 -->\n        <div class=\"metric-category\">\n          <h4>组件复用类</h4>\n          <div class=\"metrics-table\">\n            <table>\n              <thead>\n                <tr>\n                  <th>指标名称</th>\n                  <th>当前值</th>\n                  <th>目标值</th>\n                  <th>完成率</th>\n                  <th>数据来源</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr v-for=\"metric in componentMetrics\" :key=\"metric.name\">\n                  <td>{{ metric.name }}</td>\n                  <td>{{ metric.current }}</td>\n                  <td>{{ metric.target }}</td>\n                  <td>\n                    <span :class=\"['completion-rate', metric.status]\">\n                      {{ metric.completionRate }}%\n                    </span>\n                  </td>\n                  <td>{{ metric.dataSource }}</td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 成本维度统计 -->\n    <div class=\"cost-section\">\n      <h2>成本维度统计</h2>\n      \n      <!-- 一级过程指标 -->\n      <div class=\"cost-overview\">\n        <h3>一级过程指标</h3>\n        <div class=\"cost-metrics-grid\">\n          <div class=\"cost-metric-card\" v-for=\"metric in costL1Metrics\" :key=\"metric.name\">\n            <div class=\"cost-metric-header\">\n              <h4>{{ metric.name }}</h4>\n              <span class=\"cost-target\">目标: {{ metric.target }}</span>\n            </div>\n            <div class=\"cost-metric-body\">\n              <div class=\"cost-value\">\n                <span class=\"current-value\">¥{{ metric.currentSaving.toLocaleString() }}</span>\n                <span class=\"optimization-rate\" :class=\"metric.status\">\n                  {{ metric.optimizationRate }}%\n                </span>\n              </div>\n              <div class=\"cost-progress\">\n                <div class=\"progress-bar\">\n                  <div class=\"progress-fill\" :style=\"{width: metric.progress + '%'}\"></div>\n                </div>\n              </div>\n              <div class=\"cost-source\">{{ metric.dataSource }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 二级过程指标 -->\n      <div class=\"cost-detail\">\n        <h3>二级过程指标</h3>\n        <div class=\"cost-detail-table\">\n          <table>\n            <thead>\n              <tr>\n                <th>指标名称</th>\n                <th>当前节约金额</th>\n                <th>目标值</th>\n                <th>完成率</th>\n                <th>数据来源</th>\n                <th>趋势</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr v-for=\"metric in costL2Metrics\" :key=\"metric.name\">\n                <td>{{ metric.name }}</td>\n                <td>¥{{ metric.currentSaving.toLocaleString() }}</td>\n                <td>{{ metric.target }}</td>\n                <td>\n                  <span :class=\"['completion-rate', metric.status]\">\n                    {{ metric.completionRate }}%\n                  </span>\n                </td>\n                <td>{{ metric.dataSource }}</td>\n                <td>\n                  <span :class=\"['trend', metric.trend]\">\n                    {{ metric.trendText }}\n                  </span>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n\n    <!-- 重点执行指标 -->\n    <div class=\"key-metrics-section\">\n      <h2>重点执行指标</h2>\n      <div class=\"key-metrics-grid\">\n        <div class=\"key-metric-card\" v-for=\"metric in keyMetrics\" :key=\"metric.name\">\n          <div class=\"key-metric-icon\">📊</div>\n          <div class=\"key-metric-content\">\n            <h4>{{ metric.name }}</h4>\n            <div class=\"key-metric-value\">{{ metric.current }}</div>\n            <div class=\"key-metric-target\">目标: {{ metric.target }}</div>\n            <div class=\"key-metric-source\">{{ metric.dataSource }}</div>\n          </div>\n          <div class=\"key-metric-status\" :class=\"metric.status\">\n            {{ metric.statusText }}\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 数据采集状态 -->\n    <div class=\"data-collection-section\">\n      <h2>数据采集状态</h2>\n      <div class=\"collection-status\">\n        <div class=\"status-item\" v-for=\"source in dataSources\" :key=\"source.name\">\n          <div class=\"status-indicator\" :class=\"source.status\"></div>\n          <span class=\"source-name\">{{ source.name }}</span>\n          <span class=\"last-update\">最后更新: {{ source.lastUpdate }}</span>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'DashboardView',\n  data() {\n    return {\n      // 筛选器相关数据\n      selectedOrganization: 'all',\n      selectedTimeRange: '30d',\n      customStartDate: '',\n      customEndDate: '',\n      \n      // 组织列表\n      organizations: [\n        { id: 'frontend', name: '前端团队' },\n        { id: 'backend', name: '后端团队' },\n        { id: 'mobile', name: '移动端团队' },\n        { id: 'devops', name: 'DevOps团队' },\n        { id: 'qa', name: '测试团队' },\n        { id: 'design', name: '设计团队' }\n      ],\n      \n      // 北极星指标\n      totalPDSaved: 156.5,\n      monthlyPDSaved: 28.3,\n      totalCostSaved: 1250000,\n      monthlyCostSaved: 185000,\n      \n      // 提效维度一级指标\n      efficiencyL1Metrics: [\n        {\n          name: '构建提效PD',\n          current: '22.5 PD/月',\n          target: '>20 PD/月',\n          progress: 112,\n          dataSource: '构建系统、部署平台'\n        },\n        {\n          name: '问题提效PD',\n          current: '18.2 PD/月',\n          target: '>15 PD/月',\n          progress: 121,\n          dataSource: '监控系统、工单系统'\n        },\n        {\n          name: '组件提效PD',\n          current: '12.8 PD/月',\n          target: '>10 PD/月',\n          progress: 128,\n          dataSource: '代码仓库、组件库'\n        }\n      ],\n      \n      // 工程构建类指标\n      buildMetrics: [\n        {\n          name: '构建速度PD',\n          current: '9.2 PD/月',\n          target: '>8 PD/月',\n          completionRate: 115,\n          status: 'success',\n          dataSource: '构建系统日志'\n        },\n        {\n          name: '部署效率PD',\n          current: '7.8 PD/月',\n          target: '>6 PD/月',\n          completionRate: 130,\n          status: 'success',\n          dataSource: '部署平台日志'\n        },\n        {\n          name: '开发体验PD',\n          current: '5.5 PD/月',\n          target: '>4 PD/月',\n          completionRate: 137,\n          status: 'success',\n          dataSource: 'IDE插件统计'\n        }\n      ],\n      \n      // 线上问题处理类指标\n      problemMetrics: [\n        {\n          name: '发现提效PD',\n          current: '6.2 PD/月',\n          target: '>5 PD/月',\n          completionRate: 124,\n          status: 'success',\n          dataSource: '监控系统'\n        },\n        {\n          name: '定位提效PD',\n          current: '9.5 PD/月',\n          target: '>8 PD/月',\n          completionRate: 118,\n          status: 'success',\n          dataSource: '工单系统'\n        },\n        {\n          name: '解决提效PD',\n          current: '2.5 PD/月',\n          target: '>3 PD/月',\n          completionRate: 83,\n          status: 'warning',\n          dataSource: '工单系统'\n        }\n      ],\n      \n      // 组件复用类指标\n      componentMetrics: [\n        {\n          name: '开发提效PD',\n          current: '4.2 PD/月',\n          target: '>3 PD/月',\n          completionRate: 140,\n          status: 'success',\n          dataSource: '项目管理工具'\n        },\n        {\n          name: '复用节约PD',\n          current: '6.8 PD/月',\n          target: '>6 PD/月',\n          completionRate: 113,\n          status: 'success',\n          dataSource: '代码分析工具'\n        },\n        {\n          name: '维护提效PD',\n          current: '1.8 PD/月',\n          target: '>2 PD/月',\n          completionRate: 90,\n          status: 'warning',\n          dataSource: '版本管理系统'\n        }\n      ],\n      \n      // 成本维度一级指标\n      costL1Metrics: [\n        {\n          name: 'CDN节约',\n          currentSaving: 45000,\n          target: '>10%',\n          optimizationRate: 12.5,\n          progress: 125,\n          status: 'success',\n          dataSource: 'CDN服务商账单'\n        },\n        {\n          name: '计算资源节约',\n          currentSaving: 85000,\n          target: '>15%',\n          optimizationRate: 18.2,\n          progress: 121,\n          status: 'success',\n          dataSource: '云服务商账单'\n        },\n        {\n          name: '存储节约',\n          currentSaving: 28000,\n          target: '>8%',\n          optimizationRate: 9.8,\n          progress: 122,\n          status: 'success',\n          dataSource: '云服务商账单'\n        },\n        {\n          name: '带宽节约',\n          currentSaving: 27000,\n          target: '>12%',\n          optimizationRate: 14.5,\n          progress: 120,\n          status: 'success',\n          dataSource: '云服务商账单'\n        }\n      ],\n      \n      // 成本维度二级指标\n      costL2Metrics: [\n        {\n          name: '缓存节约',\n          currentSaving: 6200,\n          target: '>5,000元/月',\n          completionRate: 124,\n          status: 'success',\n          dataSource: 'CDN服务商',\n          trend: 'up',\n          trendText: '↗ +8.5%'\n        },\n        {\n          name: '压缩节约',\n          currentSaving: 3800,\n          target: '>3,000元/月',\n          completionRate: 126,\n          status: 'success',\n          dataSource: 'CDN服务商',\n          trend: 'up',\n          trendText: '↗ +12.3%'\n        },\n        {\n          name: '分发节约',\n          currentSaving: 2100,\n          target: '>2,000元/月',\n          completionRate: 105,\n          status: 'success',\n          dataSource: 'CDN服务商',\n          trend: 'stable',\n          trendText: '→ +2.1%'\n        },\n        {\n          name: 'CPU节约',\n          currentSaving: 9200,\n          target: '>8,000元/月',\n          completionRate: 115,\n          status: 'success',\n          dataSource: '云服务商',\n          trend: 'up',\n          trendText: '↗ +15.2%'\n        },\n        {\n          name: '内存节约',\n          currentSaving: 6800,\n          target: '>6,000元/月',\n          completionRate: 113,\n          status: 'success',\n          dataSource: '云服务商',\n          trend: 'up',\n          trendText: '↗ +9.8%'\n        },\n        {\n          name: '实例节约',\n          currentSaving: 3800,\n          target: '>4,000元/月',\n          completionRate: 95,\n          status: 'warning',\n          dataSource: '云服务商',\n          trend: 'down',\n          trendText: '↘ -2.5%'\n        }\n      ],\n      \n      // 重点执行指标\n      keyMetrics: [\n        {\n          name: '增量编译节约',\n          current: '68%',\n          target: '>60%',\n          dataSource: '构建系统',\n          status: 'success',\n          statusText: '达标'\n        },\n        {\n          name: '告警准确率',\n          current: '92%',\n          target: '>90%',\n          dataSource: '监控系统',\n          status: 'success',\n          statusText: '达标'\n        },\n        {\n          name: '组件使用频次',\n          current: '58次/组件',\n          target: '>50次/组件',\n          dataSource: '代码分析工具',\n          status: 'success',\n          statusText: '达标'\n        }\n      ],\n      \n      // 数据源状态\n      dataSources: [\n        {\n          name: '构建系统',\n          status: 'online',\n          lastUpdate: '2024-06-20 15:30:00'\n        },\n        {\n          name: '部署平台',\n          status: 'online',\n          lastUpdate: '2024-06-20 15:28:00'\n        },\n        {\n          name: '监控系统',\n          status: 'online',\n          lastUpdate: '2024-06-20 15:32:00'\n        },\n        {\n          name: '工单系统',\n          status: 'warning',\n          lastUpdate: '2024-06-20 14:45:00'\n        },\n        {\n          name: 'CDN服务商',\n          status: 'online',\n          lastUpdate: '2024-06-20 15:25:00'\n        },\n        {\n          name: '云服务商',\n          status: 'online',\n          lastUpdate: '2024-06-20 15:20:00'\n        }\n      ]\n    }\n  },\n  \n  methods: {\n    // 组织变更处理\n    onOrganizationChange() {\n      console.log('组织切换到:', this.selectedOrganization)\n      this.loadDataByFilters()\n    },\n    \n    // 时间范围变更处理\n    onTimeRangeChange() {\n      console.log('时间范围切换到:', this.selectedTimeRange)\n      if (this.selectedTimeRange !== 'custom') {\n        this.loadDataByFilters()\n      }\n    },\n    \n    // 自定义日期变更处理\n    onCustomDateChange() {\n      if (this.customStartDate && this.customEndDate) {\n        console.log('自定义时间范围:', this.customStartDate, '到', this.customEndDate)\n        this.loadDataByFilters()\n      }\n    },\n    \n    // 应用筛选\n    applyFilters() {\n      console.log('应用筛选条件:', {\n        organization: this.selectedOrganization,\n        timeRange: this.selectedTimeRange,\n        customStartDate: this.customStartDate,\n        customEndDate: this.customEndDate\n      })\n      this.loadDataByFilters()\n    },\n    \n    // 重置筛选\n    resetFilters() {\n      this.selectedOrganization = 'all'\n      this.selectedTimeRange = '30d'\n      this.customStartDate = ''\n      this.customEndDate = ''\n      console.log('筛选条件已重置')\n      this.loadDataByFilters()\n    },\n    \n    // 根据筛选条件加载数据\n    loadDataByFilters() {\n      // 这里可以根据筛选条件调用API获取数据\n      // 目前只是模拟数据更新\n      const orgMultiplier = this.getOrgMultiplier()\n      const timeMultiplier = this.getTimeMultiplier()\n      \n      // 更新北极星指标\n      this.totalPDSaved = (156.5 * orgMultiplier * timeMultiplier).toFixed(1)\n      this.monthlyPDSaved = (28.3 * orgMultiplier).toFixed(1)\n      this.totalCostSaved = Math.round(1250000 * orgMultiplier * timeMultiplier)\n      this.monthlyCostSaved = Math.round(185000 * orgMultiplier)\n      \n      console.log('数据已根据筛选条件更新')\n    },\n    \n    // 获取组织系数\n    getOrgMultiplier() {\n      const multipliers = {\n        'all': 1.0,\n        'frontend': 0.4,\n        'backend': 0.3,\n        'mobile': 0.15,\n        'devops': 0.1,\n        'qa': 0.03,\n        'design': 0.02\n      }\n      return multipliers[this.selectedOrganization] || 1.0\n    },\n    \n    // 获取时间系数\n    getTimeMultiplier() {\n      const multipliers = {\n        '7d': 0.25,\n        '30d': 1.0,\n        '90d': 3.0,\n        '6m': 6.0,\n        '1y': 12.0,\n        'custom': 1.0\n      }\n      return multipliers[this.selectedTimeRange] || 1.0\n    },\n    \n    // 格式化日期显示\n    formatDate(date) {\n      return new Date(date).toLocaleDateString('zh-CN')\n    }\n  },\n  \n  mounted() {\n    // 组件挂载时设置默认的自定义日期\n    const today = new Date()\n    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)\n    \n    this.customEndDate = today.toISOString().split('T')[0]\n    this.customStartDate = thirtyDaysAgo.toISOString().split('T')[0]\n  }\n}\n</script>\n\n<style scoped>\n.dashboard {\n  padding: 20px;\n  max-width: 1400px;\n  margin: 0 auto;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n}\n\n/* 页面标题样式 */\n.dashboard-header {\n  margin-bottom: 40px;\n  padding: 30px 0;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  border-radius: 12px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);\n}\n\n.header-content {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 20px;\n  text-align: center;\n}\n\n.dashboard-header h1 {\n  font-size: 2rem;\n  margin-bottom: 30px;\n  font-weight: 600;\n  color: #2c3e50;\n}\n\n/* 筛选器样式 */\n.filters-section {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  margin: 0;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  gap: 15px;\n  border: 1px solid #e9ecef;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.filter-group {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.filter-group label {\n  font-size: 14px;\n  font-weight: 500;\n  color: #495057;\n  white-space: nowrap;\n}\n\n.filter-group select {\n  padding: 8px 12px;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  background: white;\n  color: #495057;\n  font-size: 14px;\n  min-width: 120px;\n  transition: all 0.3s ease;\n}\n\n.filter-group select:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);\n}\n\n.custom-date-range {\n  display: flex;\n  gap: 15px;\n  align-items: center;\n  flex-wrap: wrap;\n}\n\n.date-input-group {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.date-input-group label {\n  font-size: 14px;\n  font-weight: 500;\n  color: #495057;\n  white-space: nowrap;\n}\n\n.date-input-group input[type=\"date\"] {\n  padding: 8px 12px;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  background: white;\n  color: #495057;\n  font-size: 14px;\n  transition: all 0.3s ease;\n}\n\n.date-input-group input[type=\"date\"]:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);\n}\n\n.apply-filters-btn,\n.reset-filters-btn {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 8px 16px;\n  border: none;\n  border-radius: 4px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.apply-filters-btn {\n  background: #28a745;\n  color: white;\n  border: 1px solid #28a745;\n}\n\n.apply-filters-btn:hover {\n  background: #218838;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);\n}\n\n.reset-filters-btn {\n  background: #dc3545;\n  color: white;\n  border: 1px solid #dc3545;\n}\n\n.reset-filters-btn:hover {\n  background: #c82333;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);\n}\n\n.btn-icon {\n  font-size: 12px;\n}\n\n\n\n/* 北极星指标样式 */\n.overview-section {\n  margin-bottom: 40px;\n}\n\n.overview-section h2 {\n  color: #2c3e50;\n  margin-bottom: 20px;\n  border-bottom: 2px solid #3498db;\n  padding-bottom: 10px;\n}\n\n.metric-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n}\n\n.metric-card {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.metric-card.efficiency {\n  border-left: 4px solid #e74c3c;\n}\n\n.metric-card.cost {\n  border-left: 4px solid #27ae60;\n}\n\n.metric-icon {\n  font-size: 3em;\n}\n\n.metric-content h3 {\n  margin: 0 0 10px 0;\n  color: #2c3e50;\n  font-size: 1.1em;\n}\n\n.metric-value {\n  font-size: 2.5em;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 5px;\n}\n\n.metric-trend {\n  font-size: 1.1em;\n  font-weight: 500;\n}\n\n.metric-trend.positive {\n  color: #27ae60;\n}\n\n/* 提效维度样式 */\n.efficiency-section {\n  margin-bottom: 40px;\n}\n\n.efficiency-section h2 {\n  color: #2c3e50;\n  margin-bottom: 20px;\n  border-bottom: 2px solid #e74c3c;\n  padding-bottom: 10px;\n}\n\n.level-one-metrics {\n  margin-bottom: 30px;\n}\n\n.level-one-metrics h3 {\n  color: #34495e;\n  margin-bottom: 15px;\n}\n\n.metrics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 20px;\n}\n\n.metric-item {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.metric-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.metric-name {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.metric-target {\n  color: #7f8c8d;\n  font-size: 0.9em;\n}\n\n.metric-progress {\n  margin-bottom: 10px;\n}\n\n.progress-bar {\n  width: 100%;\n  height: 8px;\n  background-color: #ecf0f1;\n  border-radius: 4px;\n  overflow: hidden;\n  margin-bottom: 5px;\n}\n\n.progress-fill {\n  height: 100%;\n  background-color: #e74c3c;\n  transition: width 0.3s ease;\n}\n\n.progress-text {\n  font-size: 0.9em;\n  color: #7f8c8d;\n}\n\n.metric-details {\n  font-size: 0.8em;\n  color: #95a5a6;\n}\n\n/* 二级指标表格样式 */\n.level-two-metrics h3 {\n  color: #34495e;\n  margin-bottom: 20px;\n}\n\n.metric-category {\n  margin-bottom: 30px;\n}\n\n.metric-category h4 {\n  color: #2c3e50;\n  margin-bottom: 15px;\n  padding-left: 10px;\n  border-left: 3px solid #e74c3c;\n}\n\n.metrics-table {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.metrics-table table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.metrics-table th {\n  background-color: #f8f9fa;\n  padding: 15px;\n  text-align: left;\n  font-weight: 600;\n  color: #2c3e50;\n  border-bottom: 1px solid #dee2e6;\n}\n\n.metrics-table td {\n  padding: 15px;\n  border-bottom: 1px solid #dee2e6;\n}\n\n.completion-rate {\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 0.9em;\n  font-weight: 500;\n}\n\n.completion-rate.success {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n.completion-rate.warning {\n  background-color: #fff3cd;\n  color: #856404;\n}\n\n/* 成本维度样式 */\n.cost-section {\n  margin-bottom: 40px;\n}\n\n.cost-section h2 {\n  color: #2c3e50;\n  margin-bottom: 20px;\n  border-bottom: 2px solid #27ae60;\n  padding-bottom: 10px;\n}\n\n.cost-overview h3 {\n  color: #34495e;\n  margin-bottom: 15px;\n}\n\n.cost-metrics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.cost-metric-card {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  border-left: 4px solid #27ae60;\n}\n\n.cost-metric-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.cost-metric-header h4 {\n  margin: 0;\n  color: #2c3e50;\n}\n\n.cost-target {\n  color: #7f8c8d;\n  font-size: 0.9em;\n}\n\n.cost-value {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.current-value {\n  font-size: 1.5em;\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.optimization-rate {\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-weight: 500;\n}\n\n.optimization-rate.success {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n.cost-progress .progress-fill {\n  background-color: #27ae60;\n}\n\n.cost-source {\n  font-size: 0.8em;\n  color: #95a5a6;\n  margin-top: 10px;\n}\n\n/* 成本详细表格 */\n.cost-detail {\n  margin-top: 30px;\n}\n\n.cost-detail h3 {\n  color: #34495e;\n  margin-bottom: 15px;\n}\n\n.cost-detail-table {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.trend {\n  font-weight: 500;\n}\n\n.trend.up {\n  color: #27ae60;\n}\n\n.trend.down {\n  color: #e74c3c;\n}\n\n.trend.stable {\n  color: #f39c12;\n}\n\n/* 重点执行指标样式 */\n.key-metrics-section {\n  margin-bottom: 40px;\n}\n\n.key-metrics-section h2 {\n  color: #2c3e50;\n  margin-bottom: 20px;\n  border-bottom: 2px solid #9b59b6;\n  padding-bottom: 10px;\n}\n\n.key-metrics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n}\n\n.key-metric-card {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  border-left: 4px solid #9b59b6;\n}\n\n.key-metric-icon {\n  font-size: 2em;\n}\n\n.key-metric-content {\n  flex: 1;\n}\n\n.key-metric-content h4 {\n  margin: 0 0 8px 0;\n  color: #2c3e50;\n}\n\n.key-metric-value {\n  font-size: 1.8em;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 5px;\n}\n\n.key-metric-target {\n  color: #7f8c8d;\n  font-size: 0.9em;\n  margin-bottom: 5px;\n}\n\n.key-metric-source {\n  color: #95a5a6;\n  font-size: 0.8em;\n}\n\n.key-metric-status {\n  padding: 6px 12px;\n  border-radius: 4px;\n  font-weight: 500;\n  font-size: 0.9em;\n}\n\n.key-metric-status.success {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n/* 数据采集状态样式 */\n.data-collection-section h2 {\n  color: #2c3e50;\n  margin-bottom: 20px;\n  border-bottom: 2px solid #34495e;\n  padding-bottom: 10px;\n}\n\n.collection-status {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.status-item {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 10px 0;\n  border-bottom: 1px solid #ecf0f1;\n}\n\n.status-item:last-child {\n  border-bottom: none;\n}\n\n.status-indicator {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n}\n\n.status-indicator.online {\n  background-color: #27ae60;\n}\n\n.status-indicator.warning {\n  background-color: #f39c12;\n}\n\n.status-indicator.offline {\n  background-color: #e74c3c;\n}\n\n.source-name {\n  font-weight: 500;\n  color: #2c3e50;\n  min-width: 120px;\n}\n\n.last-update {\n  color: #7f8c8d;\n  font-size: 0.9em;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .dashboard {\n    padding: 10px;\n  }\n  \n  .dashboard-header h1 {\n    font-size: 1.8rem;\n  }\n  \n  /* 筛选器响应式 */\n  .filters-section {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 15px;\n    padding: 15px;\n  }\n\n  .filter-group {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 5px;\n  }\n\n  .filter-group select {\n    min-width: auto;\n    width: 100%;\n  }\n\n  .custom-date-range {\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  .date-input-group {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 5px;\n  }\n\n  .date-input-group input[type=\"date\"] {\n    width: 100%;\n  }\n\n  .apply-filters-btn,\n  .reset-filters-btn {\n    width: 100%;\n    justify-content: center;\n  }\n  \n  .metric-cards {\n    grid-template-columns: 1fr;\n  }\n  \n  .metrics-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .cost-metrics-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .key-metrics-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .metrics-table {\n    overflow-x: auto;\n  }\n  \n  .collection-status {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n  \n  .status-item {\n    width: 100%;\n    justify-content: space-between;\n  }\n}\n\n@media (max-width: 480px) {\n  .filters-section {\n    padding: 15px;\n  }\n  \n  .filter-group label,\n  .date-input-group label {\n    font-size: 13px;\n  }\n  \n  .filter-group select,\n  .date-input-group input[type=\"date\"] {\n    font-size: 13px;\n    padding: 6px 10px;\n  }\n  \n  .apply-filters-btn,\n  .reset-filters-btn {\n    font-size: 13px;\n    padding: 6px 12px;\n  }\n}\n</style>"], "mappings": "AA8TA,eAAe;EACbA,IAAI,EAAE,eAAe;EACrBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,oBAAoB,EAAE,KAAK;MAC3BC,iBAAiB,EAAE,KAAK;MACxBC,eAAe,EAAE,EAAE;MACnBC,aAAa,EAAE,EAAE;MAEjB;MACAC,aAAa,EAAE,CACb;QAAEC,EAAE,EAAE,UAAU;QAAEP,IAAI,EAAE;MAAO,CAAC,EAChC;QAAEO,EAAE,EAAE,SAAS;QAAEP,IAAI,EAAE;MAAO,CAAC,EAC/B;QAAEO,EAAE,EAAE,QAAQ;QAAEP,IAAI,EAAE;MAAQ,CAAC,EAC/B;QAAEO,EAAE,EAAE,QAAQ;QAAEP,IAAI,EAAE;MAAW,CAAC,EAClC;QAAEO,EAAE,EAAE,IAAI;QAAEP,IAAI,EAAE;MAAO,CAAC,EAC1B;QAAEO,EAAE,EAAE,QAAQ;QAAEP,IAAI,EAAE;MAAO,EAC9B;MAED;MACAQ,YAAY,EAAE,KAAK;MACnBC,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,OAAO;MACvBC,gBAAgB,EAAE,MAAM;MAExB;MACAC,mBAAmB,EAAE,CACnB;QACEZ,IAAI,EAAE,QAAQ;QACda,OAAO,EAAE,WAAW;QACpBC,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE,GAAG;QACbC,UAAU,EAAE;MACd,CAAC,EACD;QACEhB,IAAI,EAAE,QAAQ;QACda,OAAO,EAAE,WAAW;QACpBC,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE,GAAG;QACbC,UAAU,EAAE;MACd,CAAC,EACD;QACEhB,IAAI,EAAE,QAAQ;QACda,OAAO,EAAE,WAAW;QACpBC,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE,GAAG;QACbC,UAAU,EAAE;MACd,EACD;MAED;MACAC,YAAY,EAAE,CACZ;QACEjB,IAAI,EAAE,QAAQ;QACda,OAAO,EAAE,UAAU;QACnBC,MAAM,EAAE,SAAS;QACjBI,cAAc,EAAE,GAAG;QACnBC,MAAM,EAAE,SAAS;QACjBH,UAAU,EAAE;MACd,CAAC,EACD;QACEhB,IAAI,EAAE,QAAQ;QACda,OAAO,EAAE,UAAU;QACnBC,MAAM,EAAE,SAAS;QACjBI,cAAc,EAAE,GAAG;QACnBC,MAAM,EAAE,SAAS;QACjBH,UAAU,EAAE;MACd,CAAC,EACD;QACEhB,IAAI,EAAE,QAAQ;QACda,OAAO,EAAE,UAAU;QACnBC,MAAM,EAAE,SAAS;QACjBI,cAAc,EAAE,GAAG;QACnBC,MAAM,EAAE,SAAS;QACjBH,UAAU,EAAE;MACd,EACD;MAED;MACAI,cAAc,EAAE,CACd;QACEpB,IAAI,EAAE,QAAQ;QACda,OAAO,EAAE,UAAU;QACnBC,MAAM,EAAE,SAAS;QACjBI,cAAc,EAAE,GAAG;QACnBC,MAAM,EAAE,SAAS;QACjBH,UAAU,EAAE;MACd,CAAC,EACD;QACEhB,IAAI,EAAE,QAAQ;QACda,OAAO,EAAE,UAAU;QACnBC,MAAM,EAAE,SAAS;QACjBI,cAAc,EAAE,GAAG;QACnBC,MAAM,EAAE,SAAS;QACjBH,UAAU,EAAE;MACd,CAAC,EACD;QACEhB,IAAI,EAAE,QAAQ;QACda,OAAO,EAAE,UAAU;QACnBC,MAAM,EAAE,SAAS;QACjBI,cAAc,EAAE,EAAE;QAClBC,MAAM,EAAE,SAAS;QACjBH,UAAU,EAAE;MACd,EACD;MAED;MACAK,gBAAgB,EAAE,CAChB;QACErB,IAAI,EAAE,QAAQ;QACda,OAAO,EAAE,UAAU;QACnBC,MAAM,EAAE,SAAS;QACjBI,cAAc,EAAE,GAAG;QACnBC,MAAM,EAAE,SAAS;QACjBH,UAAU,EAAE;MACd,CAAC,EACD;QACEhB,IAAI,EAAE,QAAQ;QACda,OAAO,EAAE,UAAU;QACnBC,MAAM,EAAE,SAAS;QACjBI,cAAc,EAAE,GAAG;QACnBC,MAAM,EAAE,SAAS;QACjBH,UAAU,EAAE;MACd,CAAC,EACD;QACEhB,IAAI,EAAE,QAAQ;QACda,OAAO,EAAE,UAAU;QACnBC,MAAM,EAAE,SAAS;QACjBI,cAAc,EAAE,EAAE;QAClBC,MAAM,EAAE,SAAS;QACjBH,UAAU,EAAE;MACd,EACD;MAED;MACAM,aAAa,EAAE,CACb;QACEtB,IAAI,EAAE,OAAO;QACbuB,aAAa,EAAE,KAAK;QACpBT,MAAM,EAAE,MAAM;QACdU,gBAAgB,EAAE,IAAI;QACtBT,QAAQ,EAAE,GAAG;QACbI,MAAM,EAAE,SAAS;QACjBH,UAAU,EAAE;MACd,CAAC,EACD;QACEhB,IAAI,EAAE,QAAQ;QACduB,aAAa,EAAE,KAAK;QACpBT,MAAM,EAAE,MAAM;QACdU,gBAAgB,EAAE,IAAI;QACtBT,QAAQ,EAAE,GAAG;QACbI,MAAM,EAAE,SAAS;QACjBH,UAAU,EAAE;MACd,CAAC,EACD;QACEhB,IAAI,EAAE,MAAM;QACZuB,aAAa,EAAE,KAAK;QACpBT,MAAM,EAAE,KAAK;QACbU,gBAAgB,EAAE,GAAG;QACrBT,QAAQ,EAAE,GAAG;QACbI,MAAM,EAAE,SAAS;QACjBH,UAAU,EAAE;MACd,CAAC,EACD;QACEhB,IAAI,EAAE,MAAM;QACZuB,aAAa,EAAE,KAAK;QACpBT,MAAM,EAAE,MAAM;QACdU,gBAAgB,EAAE,IAAI;QACtBT,QAAQ,EAAE,GAAG;QACbI,MAAM,EAAE,SAAS;QACjBH,UAAU,EAAE;MACd,EACD;MAED;MACAS,aAAa,EAAE,CACb;QACEzB,IAAI,EAAE,MAAM;QACZuB,aAAa,EAAE,IAAI;QACnBT,MAAM,EAAE,WAAW;QACnBI,cAAc,EAAE,GAAG;QACnBC,MAAM,EAAE,SAAS;QACjBH,UAAU,EAAE,QAAQ;QACpBU,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE;MACb,CAAC,EACD;QACE3B,IAAI,EAAE,MAAM;QACZuB,aAAa,EAAE,IAAI;QACnBT,MAAM,EAAE,WAAW;QACnBI,cAAc,EAAE,GAAG;QACnBC,MAAM,EAAE,SAAS;QACjBH,UAAU,EAAE,QAAQ;QACpBU,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE;MACb,CAAC,EACD;QACE3B,IAAI,EAAE,MAAM;QACZuB,aAAa,EAAE,IAAI;QACnBT,MAAM,EAAE,WAAW;QACnBI,cAAc,EAAE,GAAG;QACnBC,MAAM,EAAE,SAAS;QACjBH,UAAU,EAAE,QAAQ;QACpBU,KAAK,EAAE,QAAQ;QACfC,SAAS,EAAE;MACb,CAAC,EACD;QACE3B,IAAI,EAAE,OAAO;QACbuB,aAAa,EAAE,IAAI;QACnBT,MAAM,EAAE,WAAW;QACnBI,cAAc,EAAE,GAAG;QACnBC,MAAM,EAAE,SAAS;QACjBH,UAAU,EAAE,MAAM;QAClBU,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE;MACb,CAAC,EACD;QACE3B,IAAI,EAAE,MAAM;QACZuB,aAAa,EAAE,IAAI;QACnBT,MAAM,EAAE,WAAW;QACnBI,cAAc,EAAE,GAAG;QACnBC,MAAM,EAAE,SAAS;QACjBH,UAAU,EAAE,MAAM;QAClBU,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE;MACb,CAAC,EACD;QACE3B,IAAI,EAAE,MAAM;QACZuB,aAAa,EAAE,IAAI;QACnBT,MAAM,EAAE,WAAW;QACnBI,cAAc,EAAE,EAAE;QAClBC,MAAM,EAAE,SAAS;QACjBH,UAAU,EAAE,MAAM;QAClBU,KAAK,EAAE,MAAM;QACbC,SAAS,EAAE;MACb,EACD;MAED;MACAC,UAAU,EAAE,CACV;QACE5B,IAAI,EAAE,QAAQ;QACda,OAAO,EAAE,KAAK;QACdC,MAAM,EAAE,MAAM;QACdE,UAAU,EAAE,MAAM;QAClBG,MAAM,EAAE,SAAS;QACjBU,UAAU,EAAE;MACd,CAAC,EACD;QACE7B,IAAI,EAAE,OAAO;QACba,OAAO,EAAE,KAAK;QACdC,MAAM,EAAE,MAAM;QACdE,UAAU,EAAE,MAAM;QAClBG,MAAM,EAAE,SAAS;QACjBU,UAAU,EAAE;MACd,CAAC,EACD;QACE7B,IAAI,EAAE,QAAQ;QACda,OAAO,EAAE,QAAQ;QACjBC,MAAM,EAAE,SAAS;QACjBE,UAAU,EAAE,QAAQ;QACpBG,MAAM,EAAE,SAAS;QACjBU,UAAU,EAAE;MACd,EACD;MAED;MACAC,WAAW,EAAE,CACX;QACE9B,IAAI,EAAE,MAAM;QACZmB,MAAM,EAAE,QAAQ;QAChBY,UAAU,EAAE;MACd,CAAC,EACD;QACE/B,IAAI,EAAE,MAAM;QACZmB,MAAM,EAAE,QAAQ;QAChBY,UAAU,EAAE;MACd,CAAC,EACD;QACE/B,IAAI,EAAE,MAAM;QACZmB,MAAM,EAAE,QAAQ;QAChBY,UAAU,EAAE;MACd,CAAC,EACD;QACE/B,IAAI,EAAE,MAAM;QACZmB,MAAM,EAAE,SAAS;QACjBY,UAAU,EAAE;MACd,CAAC,EACD;QACE/B,IAAI,EAAE,QAAQ;QACdmB,MAAM,EAAE,QAAQ;QAChBY,UAAU,EAAE;MACd,CAAC,EACD;QACE/B,IAAI,EAAE,MAAM;QACZmB,MAAM,EAAE,QAAQ;QAChBY,UAAU,EAAE;MACd;IAEJ;EACF,CAAC;EAEDC,OAAO,EAAE;IACP;IACAC,oBAAoBA,CAAA,EAAG;MACrBC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAACjC,oBAAoB;MAC/C,IAAI,CAACkC,iBAAiB,CAAC;IACzB,CAAC;IAED;IACAC,iBAAiBA,CAAA,EAAG;MAClBH,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAChC,iBAAiB;MAC9C,IAAI,IAAI,CAACA,iBAAgB,KAAM,QAAQ,EAAE;QACvC,IAAI,CAACiC,iBAAiB,CAAC;MACzB;IACF,CAAC;IAED;IACAE,kBAAkBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAAClC,eAAc,IAAK,IAAI,CAACC,aAAa,EAAE;QAC9C6B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC/B,eAAe,EAAE,GAAG,EAAE,IAAI,CAACC,aAAa;QACrE,IAAI,CAAC+B,iBAAiB,CAAC;MACzB;IACF,CAAC;IAED;IACAG,YAAYA,CAAA,EAAG;MACbL,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBK,YAAY,EAAE,IAAI,CAACtC,oBAAoB;QACvCuC,SAAS,EAAE,IAAI,CAACtC,iBAAiB;QACjCC,eAAe,EAAE,IAAI,CAACA,eAAe;QACrCC,aAAa,EAAE,IAAI,CAACA;MACtB,CAAC;MACD,IAAI,CAAC+B,iBAAiB,CAAC;IACzB,CAAC;IAED;IACAM,YAAYA,CAAA,EAAG;MACb,IAAI,CAACxC,oBAAmB,GAAI,KAAI;MAChC,IAAI,CAACC,iBAAgB,GAAI,KAAI;MAC7B,IAAI,CAACC,eAAc,GAAI,EAAC;MACxB,IAAI,CAACC,aAAY,GAAI,EAAC;MACtB6B,OAAO,CAACC,GAAG,CAAC,SAAS;MACrB,IAAI,CAACC,iBAAiB,CAAC;IACzB,CAAC;IAED;IACAA,iBAAiBA,CAAA,EAAG;MAClB;MACA;MACA,MAAMO,aAAY,GAAI,IAAI,CAACC,gBAAgB,CAAC;MAC5C,MAAMC,cAAa,GAAI,IAAI,CAACC,iBAAiB,CAAC;;MAE9C;MACA,IAAI,CAACtC,YAAW,GAAI,CAAC,KAAI,GAAImC,aAAY,GAAIE,cAAc,EAAEE,OAAO,CAAC,CAAC;MACtE,IAAI,CAACtC,cAAa,GAAI,CAAC,IAAG,GAAIkC,aAAa,EAAEI,OAAO,CAAC,CAAC;MACtD,IAAI,CAACrC,cAAa,GAAIsC,IAAI,CAACC,KAAK,CAAC,OAAM,GAAIN,aAAY,GAAIE,cAAc;MACzE,IAAI,CAAClC,gBAAe,GAAIqC,IAAI,CAACC,KAAK,CAAC,MAAK,GAAIN,aAAa;MAEzDT,OAAO,CAACC,GAAG,CAAC,aAAa;IAC3B,CAAC;IAED;IACAS,gBAAgBA,CAAA,EAAG;MACjB,MAAMM,WAAU,GAAI;QAClB,KAAK,EAAE,GAAG;QACV,UAAU,EAAE,GAAG;QACf,SAAS,EAAE,GAAG;QACd,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE,GAAG;QACb,IAAI,EAAE,IAAI;QACV,QAAQ,EAAE;MACZ;MACA,OAAOA,WAAW,CAAC,IAAI,CAAChD,oBAAoB,KAAK,GAAE;IACrD,CAAC;IAED;IACA4C,iBAAiBA,CAAA,EAAG;MAClB,MAAMI,WAAU,GAAI;QAClB,IAAI,EAAE,IAAI;QACV,KAAK,EAAE,GAAG;QACV,KAAK,EAAE,GAAG;QACV,IAAI,EAAE,GAAG;QACT,IAAI,EAAE,IAAI;QACV,QAAQ,EAAE;MACZ;MACA,OAAOA,WAAW,CAAC,IAAI,CAAC/C,iBAAiB,KAAK,GAAE;IAClD,CAAC;IAED;IACAgD,UAAUA,CAACC,IAAI,EAAE;MACf,OAAO,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,CAAC,OAAO;IAClD;EACF,CAAC;EAEDC,OAAOA,CAAA,EAAG;IACR;IACA,MAAMC,KAAI,GAAI,IAAIH,IAAI,CAAC;IACvB,MAAMI,aAAY,GAAI,IAAIJ,IAAI,CAACG,KAAK,CAACE,OAAO,CAAC,IAAI,EAAC,GAAI,EAAC,GAAI,EAAC,GAAI,EAAC,GAAI,IAAI;IAEzE,IAAI,CAACrD,aAAY,GAAImD,KAAK,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IACrD,IAAI,CAACxD,eAAc,GAAIqD,aAAa,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EACjE;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}