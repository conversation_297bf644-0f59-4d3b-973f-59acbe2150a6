{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, normalizeClass as _normalizeClass, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  id: \"app\"\n};\nconst _hoisted_2 = {\n  class: \"top-nav\"\n};\nconst _hoisted_3 = {\n  class: \"nav-container\"\n};\nconst _hoisted_4 = {\n  class: \"nav-links\"\n};\nconst _hoisted_5 = {\n  class: \"main-content\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_router_link = _resolveComponent(\"router-link\");\n  const _component_router_view = _resolveComponent(\"router-view\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 顶部导航 \"), _createElementVNode(\"nav\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n    class: \"nav-brand\"\n  }, [_createElementVNode(\"h1\", null, \"前端基建效率与成本洞察平台\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_router_link, {\n    to: \"/\",\n    class: _normalizeClass([\"nav-link\", {\n      active: _ctx.$route.path === '/'\n    }])\n  }, {\n    default: _withCtx(() => _cache[0] || (_cache[0] = [_createTextVNode(\"统计概览\")])),\n    _: 1 /* STABLE */,\n    __: [0]\n  }, 8 /* PROPS */, [\"class\"]), _createVNode(_component_router_link, {\n    to: \"/metrics\",\n    class: _normalizeClass([\"nav-link\", {\n      active: _ctx.$route.path === '/metrics'\n    }])\n  }, {\n    default: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\"效率指标\")])),\n    _: 1 /* STABLE */,\n    __: [1]\n  }, 8 /* PROPS */, [\"class\"]), _createVNode(_component_router_link, {\n    to: \"/data-collection\",\n    class: _normalizeClass([\"nav-link\", {\n      active: _ctx.$route.path === '/data-collection'\n    }])\n  }, {\n    default: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\"数据采集\")])),\n    _: 1 /* STABLE */,\n    __: [2]\n  }, 8 /* PROPS */, [\"class\"])])])]), _createCommentVNode(\" 主要内容区域 \"), _createElementVNode(\"main\", _hoisted_5, [_createVNode(_component_router_view)])]);\n}", "map": {"version": 3, "names": ["id", "class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_router_link", "to", "_normalizeClass", "active", "_ctx", "$route", "path", "_cache", "_hoisted_5", "_component_router_view"], "sources": ["/Users/<USER>/Desktop/dev/frontend-project/src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <!-- 顶部导航 -->\n    <nav class=\"top-nav\">\n      <div class=\"nav-container\">\n        <div class=\"nav-brand\">\n          <h1>前端基建效率与成本洞察平台</h1>\n        </div>\n        <div class=\"nav-links\">\n          <router-link to=\"/\" class=\"nav-link\" :class=\"{ active: $route.path === '/' }\">统计概览</router-link>\n          <router-link to=\"/metrics\" class=\"nav-link\" :class=\"{ active: $route.path === '/metrics' }\">效率指标</router-link>\n          <router-link to=\"/data-collection\" class=\"nav-link\" :class=\"{ active: $route.path === '/data-collection' }\">数据采集</router-link>\n        </div>\n      </div>\n    </nav>\n\n    <!-- 主要内容区域 -->\n    <main class=\"main-content\">\n      <router-view/>\n    </main>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'App'\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 顶部导航样式 */\n.top-nav {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n}\n\n.nav-container {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  min-height: 70px;\n}\n\n.nav-brand h1 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: white;\n}\n\n.nav-links {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n}\n\n.nav-link {\n  color: white;\n  text-decoration: none;\n  padding: 10px 20px;\n  border-radius: 25px;\n  background: rgba(255, 255, 255, 0.1);\n  transition: all 0.3s ease;\n  font-weight: 500;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  font-size: 14px;\n}\n\n.nav-link:hover {\n  background: rgba(255, 255, 255, 0.2);\n  transform: translateY(-2px);\n}\n\n.nav-link.active {\n  background: rgba(255, 255, 255, 0.3);\n  border-color: rgba(255, 255, 255, 0.5);\n}\n\n/* 主要内容区域 */\n.main-content {\n  flex: 1;\n  padding-top: 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .nav-container {\n    flex-direction: column;\n    padding: 15px 20px;\n    gap: 15px;\n  }\n\n  .nav-brand h1 {\n    font-size: 1.2rem;\n    text-align: center;\n  }\n\n  .nav-links {\n    flex-wrap: wrap;\n    justify-content: center;\n    gap: 10px;\n  }\n\n  .nav-link {\n    padding: 8px 16px;\n    font-size: 13px;\n  }\n}\n\n@media (max-width: 480px) {\n  .nav-container {\n    padding: 10px 15px;\n  }\n\n  .nav-brand h1 {\n    font-size: 1rem;\n  }\n\n  .nav-links {\n    gap: 8px;\n  }\n\n  .nav-link {\n    padding: 6px 12px;\n    font-size: 12px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,EAAE,EAAC;AAAK;;EAENC,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAe;;EAInBA,KAAK,EAAC;AAAW;;EASpBA,KAAK,EAAC;AAAc;;;;uBAhB5BC,mBAAA,CAmBM,OAnBNC,UAmBM,GAlBJC,mBAAA,UAAa,EACbC,mBAAA,CAWM,OAXNC,UAWM,GAVJD,mBAAA,CASM,OATNE,UASM,G,0BARJF,mBAAA,CAEM;IAFDJ,KAAK,EAAC;EAAW,IACpBI,mBAAA,CAAsB,YAAlB,eAAa,E,qBAEnBA,mBAAA,CAIM,OAJNG,UAIM,GAHJC,YAAA,CAAgGC,sBAAA;IAAnFC,EAAE,EAAC,GAAG;IAACV,KAAK,EAAAW,eAAA,EAAC,UAAU;MAAAC,MAAA,EAAmBC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAAY,MAAIC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;gCAClFR,YAAA,CAA8GC,sBAAA;IAAjGC,EAAE,EAAC,UAAU;IAACV,KAAK,EAAAW,eAAA,EAAC,UAAU;MAAAC,MAAA,EAAmBC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAAmB,MAAIC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;gCAChGR,YAAA,CAA8HC,sBAAA;IAAjHC,EAAE,EAAC,kBAAkB;IAACV,KAAK,EAAAW,eAAA,EAAC,UAAU;MAAAC,MAAA,EAAmBC,IAAA,CAAAC,MAAM,CAACC,IAAI;IAAA;;sBAA2B,MAAIC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;sCAKtHb,mBAAA,YAAe,EACfC,mBAAA,CAEO,QAFPa,UAEO,GADLT,YAAA,CAAcU,sBAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}