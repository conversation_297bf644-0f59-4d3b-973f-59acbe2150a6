{"ast": null, "code": "import { createRouter, createWebHistory } from 'vue-router';\nimport Dashboard from '../views/Dashboard.vue';\nimport DataCollection from '../components/DataCollection.vue';\nconst routes = [{\n  path: '/',\n  name: 'Dashboard',\n  component: Dashboard\n}, {\n  path: '/data-collection',\n  name: 'DataCollection',\n  component: DataCollection\n}];\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n});\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "Dashboard", "DataCollection", "routes", "path", "name", "component", "router", "history", "process", "env", "BASE_URL"], "sources": ["/Users/<USER>/Desktop/dev/frontend-project/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\nimport Dashboard from '../views/Dashboard.vue'\nimport DataCollection from '../components/DataCollection.vue'\n\nconst routes = [\n  {\n    path: '/',\n    name: 'Dashboard',\n    component: Dashboard\n  },\n  {\n    path: '/data-collection',\n    name: 'DataCollection',\n    component: DataCollection\n  }\n]\n\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n})\n\nexport default router"], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,cAAc,MAAM,kCAAkC;AAE7D,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEL;AACb,CAAC,EACD;EACEG,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,gBAAgB;EACtBC,SAAS,EAAEJ;AACb,CAAC,CACF;AAED,MAAMK,MAAM,GAAGR,YAAY,CAAC;EAC1BS,OAAO,EAAER,gBAAgB,CAACS,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;EAC/CR;AACF,CAAC,CAAC;AAEF,eAAeI,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}