{"ast": null, "code": "export default {\n  name: 'App'\n};", "map": {"version": 3, "names": ["name"], "sources": ["/Users/<USER>/Desktop/dev/frontend-project/src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <!-- 顶部导航 -->\n    <nav class=\"top-nav\">\n      <div class=\"nav-container\">\n        <div class=\"nav-brand\">\n          <h1>前端基建效率与成本洞察平台</h1>\n        </div>\n        <div class=\"nav-links\">\n          <router-link to=\"/\" class=\"nav-link\" :class=\"{ active: $route.path === '/' }\">统计概览</router-link>\n          <router-link to=\"/metrics\" class=\"nav-link\" :class=\"{ active: $route.path === '/metrics' }\">效率指标</router-link>\n          <router-link to=\"/data-collection\" class=\"nav-link\" :class=\"{ active: $route.path === '/data-collection' }\">数据采集</router-link>\n        </div>\n      </div>\n    </nav>\n\n    <!-- 主要内容区域 -->\n    <main class=\"main-content\">\n      <router-view/>\n    </main>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'App'\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 顶部导航样式 */\n.top-nav {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n}\n\n.nav-container {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  min-height: 70px;\n}\n\n.nav-brand h1 {\n  margin: 0;\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: white;\n}\n\n.nav-links {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n}\n\n.nav-link {\n  color: white;\n  text-decoration: none;\n  padding: 10px 20px;\n  border-radius: 25px;\n  background: rgba(255, 255, 255, 0.1);\n  transition: all 0.3s ease;\n  font-weight: 500;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  font-size: 14px;\n}\n\n.nav-link:hover {\n  background: rgba(255, 255, 255, 0.2);\n  transform: translateY(-2px);\n}\n\n.nav-link.active {\n  background: rgba(255, 255, 255, 0.3);\n  border-color: rgba(255, 255, 255, 0.5);\n}\n\n/* 主要内容区域 */\n.main-content {\n  flex: 1;\n  padding-top: 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .nav-container {\n    flex-direction: column;\n    padding: 15px 20px;\n    gap: 15px;\n  }\n\n  .nav-brand h1 {\n    font-size: 1.2rem;\n    text-align: center;\n  }\n\n  .nav-links {\n    flex-wrap: wrap;\n    justify-content: center;\n    gap: 10px;\n  }\n\n  .nav-link {\n    padding: 8px 16px;\n    font-size: 13px;\n  }\n}\n\n@media (max-width: 480px) {\n  .nav-container {\n    padding: 10px 15px;\n  }\n\n  .nav-brand h1 {\n    font-size: 1rem;\n  }\n\n  .nav-links {\n    gap: 8px;\n  }\n\n  .nav-link {\n    padding: 6px 12px;\n    font-size: 12px;\n  }\n}\n</style>\n"], "mappings": "AAwBA,eAAe;EACbA,IAAI,EAAE;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}