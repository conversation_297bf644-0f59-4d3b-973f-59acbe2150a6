[{"/Users/<USER>/Desktop/dev/frontend-project/src/main.js": "1", "/Users/<USER>/Desktop/dev/frontend-project/src/App.vue": "2", "/Users/<USER>/Desktop/dev/frontend-project/src/router/index.js": "3", "/Users/<USER>/Desktop/dev/frontend-project/src/views/Dashboard.vue": "4", "/Users/<USER>/Desktop/dev/frontend-project/src/components/DataCollection.vue": "5", "/Users/<USER>/Desktop/dev/frontend-project/src/components/EfficiencyMetrics.vue": "6"}, {"size": 132, "mtime": 1750434751736, "results": "7", "hashOfConfig": "8"}, {"size": 2737, "mtime": 1750435517429, "results": "9", "hashOfConfig": "8"}, {"size": 636, "mtime": 1750435087053, "results": "10", "hashOfConfig": "8"}, {"size": 33141, "mtime": 1750435643936, "results": "11", "hashOfConfig": "8"}, {"size": 16517, "mtime": 1750434840963, "results": "12", "hashOfConfig": "8"}, {"size": 21697, "mtime": 1750435079973, "results": "13", "hashOfConfig": "8"}, {"filePath": "14", "messages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "16"}, "1dgf7zx", {"filePath": "17", "messages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "16"}, {"filePath": "21", "messages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "23", "messages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "25"}, {"filePath": "26", "messages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "25"}, "/Users/<USER>/Desktop/dev/frontend-project/src/main.js", [], [], "/Users/<USER>/Desktop/dev/frontend-project/src/App.vue", [], "/Users/<USER>/Desktop/dev/frontend-project/src/router/index.js", [], "/Users/<USER>/Desktop/dev/frontend-project/src/views/Dashboard.vue", [], "/Users/<USER>/Desktop/dev/frontend-project/src/components/DataCollection.vue", [], [], "/Users/<USER>/Desktop/dev/frontend-project/src/components/EfficiencyMetrics.vue", []]