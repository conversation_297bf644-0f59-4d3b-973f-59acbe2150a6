[{"/Users/<USER>/Desktop/dev/frontend-project/src/main.js": "1", "/Users/<USER>/Desktop/dev/frontend-project/src/App.vue": "2", "/Users/<USER>/Desktop/dev/frontend-project/src/components/HelloWorld.vue": "3", "/Users/<USER>/Desktop/dev/frontend-project/src/router/index.js": "4", "/Users/<USER>/Desktop/dev/frontend-project/src/views/Dashboard.vue": "5"}, {"size": 132, "mtime": 1750434751736, "results": "6", "hashOfConfig": "7"}, {"size": 338, "mtime": 1750434757516, "results": "8", "hashOfConfig": "7"}, {"size": 2025, "mtime": 1750434500412, "results": "9", "hashOfConfig": "7"}, {"size": 314, "mtime": 1750434747600, "results": "10", "hashOfConfig": "7"}, {"size": 24003, "mtime": 1750434742101, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "14"}, "1dgf7zx", {"filePath": "15", "messages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "17", "messages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "21", "messages": "22", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Desktop/dev/frontend-project/src/main.js", [], [], "/Users/<USER>/Desktop/dev/frontend-project/src/App.vue", [], "/Users/<USER>/Desktop/dev/frontend-project/src/components/HelloWorld.vue", [], "/Users/<USER>/Desktop/dev/frontend-project/src/router/index.js", [], "/Users/<USER>/Desktop/dev/frontend-project/src/views/Dashboard.vue", ["23"], {"ruleId": "24", "severity": 2, "message": "25", "line": 261, "column": 9, "nodeType": "26", "messageId": "27", "endLine": 261, "endColumn": 20}, "vue/multi-word-component-names", "Component name \"Dashboard\" should always be multi-word.", "Literal", "unexpected"]