#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/dev/frontend-project/node_modules/.pnpm/@vue+cli-service@5.0.8_@vue+compiler-sfc@3.5.17_lodash@4.17.21_vue@3.5.17_webpack-sources@3.3.3/node_modules/@vue/cli-service/bin/node_modules:/Users/<USER>/Desktop/dev/frontend-project/node_modules/.pnpm/@vue+cli-service@5.0.8_@vue+compiler-sfc@3.5.17_lodash@4.17.21_vue@3.5.17_webpack-sources@3.3.3/node_modules/@vue/cli-service/node_modules:/Users/<USER>/Desktop/dev/frontend-project/node_modules/.pnpm/@vue+cli-service@5.0.8_@vue+compiler-sfc@3.5.17_lodash@4.17.21_vue@3.5.17_webpack-sources@3.3.3/node_modules/@vue/node_modules:/Users/<USER>/Desktop/dev/frontend-project/node_modules/.pnpm/@vue+cli-service@5.0.8_@vue+compiler-sfc@3.5.17_lodash@4.17.21_vue@3.5.17_webpack-sources@3.3.3/node_modules:/Users/<USER>/Desktop/dev/frontend-project/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/dev/frontend-project/node_modules/.pnpm/@vue+cli-service@5.0.8_@vue+compiler-sfc@3.5.17_lodash@4.17.21_vue@3.5.17_webpack-sources@3.3.3/node_modules/@vue/cli-service/bin/node_modules:/Users/<USER>/Desktop/dev/frontend-project/node_modules/.pnpm/@vue+cli-service@5.0.8_@vue+compiler-sfc@3.5.17_lodash@4.17.21_vue@3.5.17_webpack-sources@3.3.3/node_modules/@vue/cli-service/node_modules:/Users/<USER>/Desktop/dev/frontend-project/node_modules/.pnpm/@vue+cli-service@5.0.8_@vue+compiler-sfc@3.5.17_lodash@4.17.21_vue@3.5.17_webpack-sources@3.3.3/node_modules/@vue/node_modules:/Users/<USER>/Desktop/dev/frontend-project/node_modules/.pnpm/@vue+cli-service@5.0.8_@vue+compiler-sfc@3.5.17_lodash@4.17.21_vue@3.5.17_webpack-sources@3.3.3/node_modules:/Users/<USER>/Desktop/dev/frontend-project/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@vue/cli-service/bin/vue-cli-service.js" "$@"
else
  exec node  "$basedir/../@vue/cli-service/bin/vue-cli-service.js" "$@"
fi
